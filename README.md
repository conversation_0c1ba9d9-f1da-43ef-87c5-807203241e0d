# Korapay Business

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

Proper documentation on app structure, contribution and best practices can be found in this [wiki]https://github.com/korapay/wiki/tree/master/Frontend%20Bible/Admin%20Dashboard%20(business-admin-fe)

**It is important you go through the wiki properly before working on this repository.**

## Setup

- Create a file called `.env.development.local` based off the `env.sample` file in the root folder
- Install all required npm packages by running: `yarn` or `npm i`
- Start up the development server by running: `yarn start:dev` or `npm run start:dev`

## Contributing

- Run `yarn gen` or `npm run gen` to create components on your terminal
- This would call a CLI tool to help you create components that work with the defined folder structure.
- These components are created based off the "templates" folder in the root directory

## Folder structure

```bash

📦business-fe
 ┃
 ┣ 📂github # Github configuration
 ┣ 📂config # Config folder for jest
 ┣ 📂public # Static files folder
 ┃
 ┣ 📂src
 ┃ ┣ 📂assets
 ┃ ┃ ┣ 📂fonts
 ┃ ┃ ┃ ┗ # Korapay fonts - Averta PE and OCR
 ┃ ┃ ┗ 📂img
 ┃ ┃   ┣ 📂dashboard
 ┃ ┃   ┃ ┗ # Images used in the dashboard container
 ┃ ┃   ┣ 📂logos
 ┃ ┃   ┃ ┗ # Korapay logos
 ┃ ┃   ┗ # Some miscellaneous images
 ┃ ┃
 ┃ ┣ 📂containers
 ┃ ┃ ┣ 📂Auth
 ┃ ┃ ┃ ┣ 📂Route Level Page eg SignIn
 ┃ ┃ ┃ ┃ ┗ 📜index.jsx
 ┃ ┃ ┃ ┣ 📂Shared
 ┃ ┃ ┃ ┃ ┗ # Any shared component for auth pages
 ┃ ┃ ┃ ┗ 📜index.jsx
 ┃ ┃ ┃
 ┃ ┃ ┣ 📂Dashboard
 ┃ ┃ ┃ ┣ 📂Route Level Page eg Settings
 ┃ ┃ ┃ ┃  ┗ 📂 # Any sub route level page eg Settings/Business
 ┃ ┃ ┃ ┃    ┃
 ┃ ┃ ┃ ┃    ┗ 📂components # React files that cannot be routed to eg Bank Details in Settings/Business
 ┃ ┃ ┃ ┃      ┣ 📜index.jsx
 ┃ ┃ ┃ ┃      ┗ 📜index.scss
 ┃ ┃ ┃ ┣ 📂Shared
 ┃ ┃ ┃ ┃ ┗ # Any shared component for Dashboard pages
 ┃ ┃ ┃ ┣ 📜index.jsx
 ┃ ┃ ┃ ┗ 📜index.scss
 ┃ ┃ ┃
 ┃ ┃ ┗ 📂Shared
 ┃ ┃   ┗ # App wide shared components
 ┃ ┃
 ┃ ┣ 📂services
 ┃ ┃ ┣ 📜api-services.js
 ┃ ┃ ┣ 📜error-services.js
 ┃ ┃ ┗ 📜storage-services.js
 ┃ ┃
 ┃ ┣ 📂store
 ┃ ┃ ┃ # Redux store
 ┃ ┃ ┗ 📜index.js
 ┃ ┃
 ┃ ┣ 📂styles
 ┃ ┃ ┣ 📂base
 ┃ ┃ ┃ ┗ #Korapay custom styles for external pages
 ┃ ┃ ┃
 ┃ ┃ ┣ 📂kpy-custom
 ┃ ┃ ┃ ┗ # Korapay custom styles for the dashboard
 ┃ ┃ ┃
 ┃ ┃ ┣ 📂theme
 ┃ ┃ ┃ ┣ 📂bootstrap-override
 ┃ ┃ ┃ ┃ ┗ # Theme bootstrap overrides
 ┃ ┃ ┃ ┗ # Other theme styles
 ┃ ┃ ┃
 ┃ ┃ ┗ 📜main.scss # Styles entry file
 ┃ ┃
 ┃ ┣ 📂utils
 ┃ ┃ ┣ 📜formats.js # Text, date and amount formatting
 ┃ ┃ ┣ 📜helmet-config.jsx # Base configuration for react helmet
 ┃ ┃ ┣ 📜history.js # Manage the history API
 ┃ ┃ ┣ 📜index.js # Entry point that exports everything in the utils folder
 ┃ ┃ ┣ 📜logger.js # Logger utility file
 ┃ ┃ ┣ 📜mixpanel.js # Mixpanel to handle tracking
 ┃ ┃ ┣ 📜request-middleware.js # Attach some functionality to redux actions
 ┃ ┃ ┣ 📜inputValidationRules.js # Validation for email, password, phone and URL
 ┃ ┃ ┗ 📜utilFunctions.js # Miscellaneous utility functions that are reused across the app
 ┃ ┃
 ┃ ┣ 📜App.jsx
 ┃ ┣ 📜App.spec.js
 ┃ ┣ 📜index.jsx
 ┃ ┗ 📜serviceWorker.js
 ┃
 ┣ 📂templates # Template folder that defines the default pages to be created when bootstrapping components
 ┃ ┣ 📜component.hbs
 ┃ ┣ 📜page.hbs
 ┃ ┣ 📜styles.hbs
 ┃ ┗ 📜test.hbs
 ┃
 ┃ # App config
 ┣ 📜.editorconfig
 ┣ 📜.gitignore
 ┣ 📜env.sample
 ┣ 📜jsconfig.json
 ┣ 📜webpack.config.js
 ┣ 📜plopfile.js
 ┃
 ┃ # Linting
 ┣ 📜.eslintrc
 ┣ 📜.huskyrc
 ┣ 📜.lintstagedrc
 ┣ 📜.prettierignore
 ┣ 📜.prettierrc
 ┣ 📜.stylelintrc
 ┃
 ┃ # DevOPs
 ┣ 📜sonar.json
 ┃
 ┃ # App setup
 ┣ 📜app.js
 ┣ 📜server.js
 ┣ 📜package.json
 ┃
 ┗ 📜README.md


## Environment Variables
```

APP_PORT
VITE_REDIRECT_URI
VITE_CORE_API_BASE
VITE_MIDDLEWARE_API_BASE

```

### Logging errors

Do not use `console.log` statements in the application.
Instead use the [`Logger` utility](src/utils/logger) through `import { Logger } from '+utils';`

- If a global loader is needed, ensure that `noLoader` is either not set in the `REQUEST` action or that it is set to `false` in the `REQUEST` action
- If a global loader is not needed, ensure that `noLoader` is set to true in the `REQUEST` action.

## Available Scripts

In the project directory, you can run:

### `npm start:dev`

Runs the app in the development mode.<br>
Open [http://localhost:4200](http://localhost:4200) to view it in the browser.

The page will reload if you make edits.<br>
You will also see any lint errors in the console.

Happy hacking 🚀
```
