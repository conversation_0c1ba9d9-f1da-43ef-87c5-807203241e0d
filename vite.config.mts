/// <reference types="vitest" />
import { dirname } from 'path';
import { fileURLToPath } from 'url';
import react from '@vitejs/plugin-react';
import { esbuildCommonjs } from '@originjs/vite-plugin-commonjs';
import { defineConfig, loadEnv } from 'vite';
import viteCompression from 'vite-plugin-compression';
import viteImagemin from 'vite-plugin-imagemin';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';

// eslint-disable-next-line no-underscore-dangle
const __dirname = dirname(fileURLToPath(import.meta.url));

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), 'REACT_APP_');

  return {
    plugins: [
      react(),
      svgr(),
      tsconfigPaths(),
      viteCompression(),
      viteImagemin({
        gifsicle: {
          optimizationLevel: 7,
          interlaced: false
        },
        optipng: {
          optimizationLevel: 7
        },
        mozjpeg: {
          quality: 20
        },
        pngquant: {
          quality: [0.8, 0.9],
          speed: 4
        },
        svgo: {
          plugins: [
            {
              name: 'removeViewBox'
            },
            {
              name: 'removeEmptyAttrs',
              active: false
            }
          ]
        }
      })
    ],
    server: {
      port: 4200,
      strictPort: true
    },
    define: {
      'process.env': env,
      global: 'window'
    },
    json: {
      stringify: true
    },
    css: {
      preprocessorOptions: {
        scss: {
          includePaths: ['src']
        }
      }
    },
    optimizeDeps: {
      esbuildOptions: {
        plugins: [esbuildCommonjs(['react-date-picker'])]
      },
      include: ['axios']
    },
    resolve: {
      alias: {
        'react-native': 'react-native-web',
        '+config': `${__dirname}/config`,
        '+dashboard': `${__dirname}/src/containers/Dashboard/`,
        '+shared': `${__dirname}/src/containers/Dashboard/Shared`,
        '+auth': `${__dirname}/src/containers/Auth/`,
        '+store': `${__dirname}/src/store/`,
        '+redux': `${__dirname}/redux/`,
        '+assets': `${__dirname}/src/assets/`,
        '+styles': `${__dirname}/src/styles`,
        styles: `${__dirname}/src/styles/`,
        '+utils': `${__dirname}/src/utils/`,
        '+services': `${__dirname}/src/services/`,
        '+constants': `${__dirname}/src/constants/`,
        '+hooks': `${__dirname}/src/hooks/`,
        '+types': `${__dirname}/src/types/`,
        '+mock': `${__dirname}/src/__mock__/`
      }
    },
    build: {
      target: ['es2018', 'chrome61', 'edge18', 'firefox60', 'safari16'],
      minify: true,
      brotliSize: true,
      chunkSizeWarningLimit: 20000,
      sourcemap: true,
      manifest: true,
      rollupOptions: {
        output: {
          manualChunks: { axios: ['axios'], react: ['react'], 'react-dom': ['react-dom'] }
        }
      }
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/test-setup.ts',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'json-summary', 'json', 'html'],
        branches: 26,
        functions: 21,
        lines: 31,
        statements: 31
      }
    }
  };
});
