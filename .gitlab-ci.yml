image: node:8
variables:
  DOCKER_TLS_CERTDIR: ''
  AWS_ECR: 711800501631.dkr.ecr.eu-west-1.amazonaws.com

  PROD_EKS: 01-ares-eks
  STAGING_EKS: 01-poseidon-eks
  REVIEW_EKS: 01-poseidon1-eks

  SA_PROD_EKS: $SA_01_ares_eks
  SA_STAGING_EKS: $SA_01_poseidon1_eks
  SA_REVIEW_EKS: $SA_01_poseidon1_eks

  ENVNAME_PROD_EKS: env.production
  ENVNAME_STAGING_EKS: env.staging
  ENVNAME_REVIEW_EKS: env.review

  PORT: 6100

  PROD_MEMORY: 200Mi
  STAGING_MEMORY: 200Mi
  REVIEW_MEMORY: 200Mi

  DATADOG_API_KEY: $DATADOG_API_KEY
  DATADOG_SITE: $DATADOG_SITE

  # If CPU limit is greater than MAX_CPU_LIMIT, system will create more pods within the MAX_POD_LIMIT.
  # If the CPU is below MAX_CPU_LIMIT, system will delete pods to the limit of REPLICAS
  PROD_MAX_CPU_LIMIT: 70
  PROD_MAX_POD_LIMIT: 10
  STAGING_MAX_CPU_LIMIT: 70
  STAGING_MAX_POD_LIMIT: 4
  REVIEW_MAX_CPU_LIMIT: 70
  REVIEW_MAX_POD_LIMIT: 3

  PROD_CPU: 100m
  STAGING_CPU: 100m
  REVIEW_CPU: 100m

  PROD_REPLICAS: 2
  STAGING_REPLICAS: 1
  REVIEW_REPLICAS: 1

cache:
  paths:
    - node_modules/
    - coverage/

stages:
  - test
  - build
  - deploy

build_production:
  stage: build
  needs: []
  environment:
    name: production
  image: weezyval/base-image-v2
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
  script:
    - sed -i "s/{{PORT}}/$PORT/g" Dockerfile
    - aws configure set region eu-west-1
    - $(aws ecr get-login --no-include-email --region eu-west-1 | sed 's|https://||')
    - aws ecr describe-repositories --repository-names ${CI_PROJECT_NAME} || aws ecr create-repository --repository-name ${CI_PROJECT_NAME}
    - >
      docker build
      -t $AWS_ECR/$CI_PROJECT_NAME:1.0.0-$CI_JOB_ID .
      --build-arg S3CONFIGUSER=$S3CONFIGUSER
      --build-arg S3CONFIGPASS=$S3CONFIGPASS
      --build-arg ENVNAME=$ENVNAME_PROD_EKS
      --build-arg S3URL=$CI_PROJECT_NAME-production
      --build-arg DATADOG_API_KEY=$DATADOG_API_KEY
      --build-arg DATADOG_SITE=$DATADOG_SITE
    - docker push $AWS_ECR/$CI_PROJECT_NAME:1.0.0-$CI_JOB_ID
    - cp deploy-production/deployment.yaml deployment.yaml
    - sed -i "s/{{IMAGE_VERSION}}/1.0.0-$CI_JOB_ID/g" deployment.yaml
    - sed -i "s/{{project}}/$CI_PROJECT_NAME/g" deployment.yaml
    - sed -i "s/{{eks}}/$PROD_EKS/g" deployment.yaml

  artifacts:
    paths:
      - deployment.yaml
  tags:
    - deploycicd
  only:
    - production
    - production-new
    - production-debug

build_staging:
  stage: build
  environment:
    name: staging
  image: weezyval/base-image-v2
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
  script:
    - sed -i "s/{{PORT}}/$PORT/g" Dockerfile
    - aws configure set region eu-west-1
    - $(aws ecr get-login --no-include-email --region eu-west-1 | sed 's|https://||')
    - aws ecr describe-repositories --repository-names ${CI_PROJECT_NAME} || aws ecr create-repository --repository-name ${CI_PROJECT_NAME}
    - >
      docker build
      -t $AWS_ECR/$CI_PROJECT_NAME:1.0.0-$CI_JOB_ID .
      --build-arg S3CONFIGUSER=$S3CONFIGUSER
      --build-arg S3CONFIGPASS=$S3CONFIGPASS
      --build-arg ENVNAME=$ENVNAME_STAGING_EKS
      --build-arg S3URL=$CI_PROJECT_NAME-staging
    - docker push $AWS_ECR/$CI_PROJECT_NAME:1.0.0-$CI_JOB_ID
    - cp deploy-staging/deployment.yaml deployment.yaml
    - sed -i "s/{{IMAGE_VERSION}}/1.0.0-$CI_JOB_ID/g" deployment.yaml
    - sed -i "s/{{project}}/$CI_PROJECT_NAME/g" deployment.yaml
    - sed -i "s/{{eks}}/$STAGING_EKS/g" deployment.yaml

  artifacts:
    paths:
      - deployment.yaml
  tags:
    - deploycicd
  only:
    - staging-temp

build_review:
  stage: build
  environment:
    name: review
  image: weezyval/base-image-v2
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
  script:
    - sed -i "s/{{PORT}}/$PORT/g" Dockerfile
    - aws configure set region eu-west-1
    - $(aws ecr get-login --no-include-email --region eu-west-1 | sed 's|https://||')
    - aws ecr describe-repositories --repository-names ${CI_PROJECT_NAME} || aws ecr create-repository --repository-name ${CI_PROJECT_NAME}
    - >
      docker build
      -t $AWS_ECR/$CI_PROJECT_NAME:1.0.0-$CI_JOB_ID .
      --build-arg S3CONFIGUSER=$S3CONFIGUSER
      --build-arg S3CONFIGPASS=$S3CONFIGPASS
      --build-arg ENVNAME=$ENVNAME_REVIEW_EKS
      --build-arg S3URL=$CI_PROJECT_NAME-review
    - docker push $AWS_ECR/$CI_PROJECT_NAME:1.0.0-$CI_JOB_ID
    - cp deploy-review/deployment.yaml deployment.yaml
    - sed -i "s/{{IMAGE_VERSION}}/1.0.0-$CI_JOB_ID/g" deployment.yaml
    - sed -i "s/{{project}}/$CI_PROJECT_NAME/g" deployment.yaml
    - sed -i "s/{{eks}}/$REVIEW_EKS/g" deployment.yaml

  artifacts:
    paths:
      - deployment.yaml
  tags:
    - deploycicd
  only:
    - review-temp

deploy_2_production:
  stage: deploy
  environment:
    name: production
  image: weezyval/base-image-v2
  script:
    - aws eks --region eu-west-1 update-kubeconfig --name $PROD_EKS

    - sed -i "s/{{PORT}}/$PORT/g" deployment.yaml
    - sed -i "s/{{memory}}/$PROD_MEMORY/g" deployment.yaml
    - sed -i "s/{{cpu}}/$PROD_CPU/g" deployment.yaml
    - sed -i "s/{{replicas}}/$PROD_REPLICAS/g" deployment.yaml
    - kubectl apply -f deployment.yaml --token=$SA_PROD_EKS
    - kubectl delete hpa $CI_PROJECT_NAME --token=$SA_PROD_EKS
    - kubectl autoscale deployment $CI_PROJECT_NAME --cpu-percent=$PROD_MAX_CPU_LIMIT --min=$PROD_REPLICAS --max=$PROD_MAX_POD_LIMIT --token=$SA_PROD_EKS

  artifacts:
    paths:
      - deployment.yaml
  tags:
    - deploycicd
  when: manual
  only:
    - production
    - production-new
    - production-debug

deploy_2_staging:
  stage: deploy
  environment:
    name: staging
  image: weezyval/base-image-v2
  script:
    - aws eks --region eu-west-1 update-kubeconfig --name $STAGING_EKS

    - sed -i "s/{{PORT}}/$PORT/g" deployment.yaml
    - sed -i "s/{{memory}}/$STAGING_MEMORY/g" deployment.yaml
    - sed -i "s/{{cpu}}/$STAGING_CPU/g" deployment.yaml
    - sed -i "s/{{replicas}}/$STAGING_REPLICAS/g" deployment.yaml
    - kubectl apply -f deployment.yaml --token=$SA_STAGING_EKS
    - kubectl delete hpa $CI_PROJECT_NAME --token=$SA_STAGING_EKS
    - kubectl autoscale deployment $CI_PROJECT_NAME --cpu-percent=$STAGING_MAX_CPU_LIMIT --min=$STAGING_REPLICAS --max=$STAGING_MAX_POD_LIMIT --token=$SA_STAGING_EKS

  artifacts:
    paths:
      - deployment.yaml
  tags:
    - deploycicd
  only:
    - staging-temp

deploy_2_review:
  stage: deploy
  environment:
    name: review
  image: weezyval/base-image-v2
  script:
    - aws eks --region eu-west-1 update-kubeconfig --name $REVIEW_EKS

    - sed -i "s/{{PORT}}/$PORT/g" deployment.yaml
    - sed -i "s/{{memory}}/$REVIEW_MEMORY/g" deployment.yaml
    - sed -i "s/{{cpu}}/$REVIEW_CPU/g" deployment.yaml
    - sed -i "s/{{replicas}}/$REVIEW_REPLICAS/g" deployment.yaml
    - kubectl apply -f deployment.yaml --token=$SA_REVIEW_EKS
    - kubectl delete hpa $CI_PROJECT_NAME --token=$SA_REVIEW_EKS
    - kubectl autoscale deployment $CI_PROJECT_NAME --cpu-percent=$REVIEW_MAX_CPU_LIMIT --min=$REVIEW_REPLICAS --max=$REVIEW_MAX_POD_LIMIT --token=$SA_REVIEW_EKS

  artifacts:
    paths:
      - deployment.yaml
  tags:
    - deploycicd
  only:
    - review-temp

sonarqube:
  stage: test
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: ['']
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar' # Defines the location of the analysis task cache
    GIT_DEPTH: '0' # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  tags:
    - deploycicd
  only:
    - review
    - staging
    - production

include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
