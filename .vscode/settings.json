{"emmet.triggerExpansionOnTab": true, "editor.quickSuggestions": {"strings": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.detectIndentation": false, "editor.insertSpaces": true, "eslint.validate": ["javascript", "typescript", "typescriptreact", "javascriptreact"], "eslint.options": {"overrideConfigFile": ".eslintrc"}, "stylelint.enable": true, "files.associations": {".stylelintrc": "json"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll": "explicit", "source.organizeImports": "explicit", "source.sortImports": "explicit", "source.removeUnusedImports": "explicit", "source.addMissingImports.ts": "explicit"}, "editor.formatOnSave": true, "typescript.tsdk": "node_modules/typescript/lib", "files.exclude": {"**/dist*": false, "**/node_modules*": false}, "editor.tabCompletion": "on", "diffEditor.codeLens": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}