#### WHY THIS PR?

Explain the business/product/technical requirements for the task.

#### WHAT DOES THIS PR DO?

Give an overview of the technical implementations of the PR.

#### LINK TO FEATURE TEST CASE (create one [here](https://www.notion.so/1a7f8f1b79098023ab2cd1e13af5f089?v=1a7f8f1b790980c29ce1000c3921b050))

Provide the link to the feature test case for this task

#### HOW CAN THIS PR BE TESTED?

Give information on how the implemented changes can be tested by the reviewer.

#### THINGS YOU MAY WANT THE REVIEWER TO NOTE.

Give information about things that might not be obvious from the PR.

#### CHECKLIST.

Check list is based on [Documentaion](https://www.notion.so/PR-Review-Checklist-135f8f1b790980cbbf32f66c2bd0c89b)

<details>
<summary>Code Reviewer Checklist</summary>

**Best Practices**

- [ ] Reviewer 1
- [ ] Reviewer 2

**Styling**

- [ ] Reviewer 1
- [ ] Reviewer 2

**TypeScript Types and Interfaces**

- [ ] Reviewer 1
- [ ] Reviewer 2

</details>

<details>
<summary>Visual Reviewer Checklist</summary>

- [ ] Design Consistency
- [ ] Functionality
- [ ] User Experience (UX)
- [ ] Performance
- [ ] Cross-Browser Compatibility
- [ ] Security

</details>
