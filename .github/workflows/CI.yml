name: Admin Dashboard CI

on:
  push:
    branches: [staging, review, production, '*-base']
  pull_request:
     branches: [staging, review, production, '*-base']


jobs:
  build:
    name: 'Build'
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - name: Send Slack Notification
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,commit,repo,ref,author
          custom_payload: |
            {
              text: ':mag_right: CI process starting :bug:',
              attachments: [{
                text: `${process.env.AS_WORKFLOW}\n\n(${process.env.AS_COMMIT}) of ${process.env.AS_REPO}@${process.env.AS_REF} by ${process.env.AS_AUTHOR}`,
                author_name: '${{ secrets.ACTION_SLACK_AUTHOR_NAME }}'
              }]
            }
          channel: ${{ secrets.SLACK_CHANNEL_DEPLOY }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()

      - uses: actions/checkout@v3
      - name: Test on Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run tests
        run: npm run test
        env:
          CI: true

      - name:  'Report Coverage'
        if: always()
        uses:  davelosert/vitest-coverage-report-action@v2

      - name: Send Slack Notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,ref,message,author,eventName,workflow
          author_name: '${{ secrets.ACTION_SLACK_AUTHOR_NAME }}'
          channel: ${{ secrets.SLACK_CHANNEL_DEPLOY }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()
