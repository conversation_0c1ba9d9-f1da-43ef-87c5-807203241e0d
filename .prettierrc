{"printWidth": 140, "trailingComma": "none", "tabWidth": 2, "semi": true, "singleQuote": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrder": ["^(react/(.*)$)|^(react$)", ".*react.*", "<THIRD_PARTY_MODULES>", "", "^\\+(?!.*\\.(png|svg|scss)$).*", "", "^(\\+)(config|dashboard|external|auth|shared|store|utils|services|constants|hooks|mock|zustandStore|types|containers)(/.*)?$", "", "^[.]", "^(?!.*[.]scss$)[./].*$", "", ".png$", ".svg$", ".scss$"], "plugins": ["@ianvs/prettier-plugin-sort-imports"], "importOrderMergeDuplicateImports": true, "importOrderCombineTypeAndValueImports": true, "importOrderSortSpecifiers": true, "importOrderCaseInsensitive": true, "importOrderBuiltinModulesToTop": true, "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "avoid"}