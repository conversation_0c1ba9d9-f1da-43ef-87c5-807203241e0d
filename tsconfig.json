{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "esnext"], "types": ["vite/client", "vite-plugin-svgr/client", "node", "vitest/globals", "@testing-library/jest-dom"], "noImplicitAny": true, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "baseUrl": ".", "paths": {"+*": ["src/*"], "+store/*": ["src/store/*"], "+assets": ["src/assets"], "+styles": ["src/styles"], "+utils": ["src/utils"], "+services/*": ["src/services/*"], "+shared/*": ["src/containers/Dashboard/Shared/*"], "+dashboard": ["src/containers/Dashboard"], "+dashboard/*": ["src/containers/Dashboard/*"], "+auth": ["src/containers/Auth"], "+hooks": ["src/hooks"], "+types": ["src/types"], "+mock/*": ["src/__mock__/*"], "+zustandStore": ["src/zustandStore"]}}, "include": ["src", "vite.config.mts", "**/*.ts", "**/*.tsx", "**/*.js", "src/**/*.tsx", "src/**/*.ts", "src/test-setup.tsx"], "exclude": ["node_modules"]}