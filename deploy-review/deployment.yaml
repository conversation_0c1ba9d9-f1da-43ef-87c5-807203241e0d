apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{project}}
  labels:
    app: {{project}}

spec:
  replicas: {{replicas}}
  selector:
     matchLabels:
       app: {{project}}
  strategy:
    rollingUpdate:
      maxSurge: 20%
      maxUnavailable: 1
  minReadySeconds: 60
  template:
    metadata:
      labels:
        app: {{project}}
    spec:
      containers:
        - name: {{project}}
          image: 711800501631.dkr.ecr.eu-west-1.amazonaws.com/{{project}}:{{IMAGE_VERSION}}
          lifecycle:
            preStop:
              exec:
                command:
                - sh
                - -c
                - "sleep 15"
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /
              port: {{PORT}}
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 1
          ports:
            - containerPort: {{PORT}}
          resources:
            requests:
              cpu: {{cpu}}
              memory: "{{memory}}"
            limits:
              cpu: {{cpu}}
              memory: "{{memory}}"
---
apiVersion: v1
kind: Service
metadata:
  name: {{project}}
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 80
      targetPort: {{PORT}}
  selector:
    app: {{project}}

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{project}}
spec:
  rules:
  - host: {{project}}.{{eks}}-korapay.com
    http:
      paths:
      - backend:
          serviceName: {{project}}
          servicePort: 80
        path: /
