module.exports = (
  /** @type {import('plop').NodePlopAPI} */
  plop
) => {
  plop.setGenerator('create', {
    prompts: [
      {
        type: 'input',
        name: 'type',
        message: 'Is this a Component (c) or a Page (p) ?',
        validate: value => {
          if (value === 'c' || value === 'p') return true;
          return 'Please try again with a valid type ';
        }
      },
      {
        type: 'input',
        name: 'name',
        message: answers => `What's the name of this ${answers.type === 'c' ? 'component' : 'page'}?`
      },
      {
        type: 'input',
        name: 'path',
        message: answers =>
          `Where should this ${
            answers.type === 'c' ? 'component' : 'page'
          } be ? \n Example: 'Dashboard/Shared' - (container/...path-to-parent-folder) \n`,
        validate: (input, answers) => {
          return new Promise((resolve, reject) => {
            const allowedContainers = ['Dashboard', 'External', 'Shared', 'Auth'];
            const container = input.split('/');
            const lastPath = container[container.length - 1].toLowerCase();

            // Add validation for where a file can be created according to the file structure
            if (!allowedContainers.includes(container[0])) reject(new Error('❌ Invalid container input. Try again'));

            if (answers.type === 'p' && lastPath === 'shared')
              reject(new Error('😏 Only components can be created in shared folders. Try again'));

            if (answers.type === 'c' && !(lastPath === 'shared' || lastPath === 'components'))
              reject(new Error('😏 You can only create a component in either a "shared" or "component" folder. Try again'));

            resolve(true);
          });
        }
      }
    ],
    actions: data => {
      const actions = [];
      switch (data.type) {
        case 'c':
          actions.push(
            {
              // Create file
              type: 'add',
              templateFile: 'templates/component.hbs',
              skipIfExists: true,
              path: 'src/containers/{{path}}/{{pascalCase  name}}.jsx'
            },
            {
              // Add the central scss file if it does not exist
              type: 'add',
              templateFile: 'templates/styles.hbs',
              skipIfExists: true,
              path: 'src/containers/{{path}}/index.scss'
            },
            {
              // Add the class to the central scss file
              type: 'append',
              template: `\n// Styles for the {{pascalCase name}} component: \n.{{dashCase name}}__comp {}`,
              path: 'src/containers/{{path}}/index.scss'
            },
            {
              // Add the appropriate test file is it does not exist
              type: 'add',
              templateFile: 'templates/test.hbs',
              skipIfExists: true,
              path: 'src/containers/{{path}}/__tests__/{{camelCase  name}}.spec.js'
            },
            {
              type: 'modify',
              path: 'src/containers/{{path}}/__tests__/{{camelCase  name}}.spec.js',
              pattern: /## replace import here ##/gi,
              template: "// import {{pascalCase name}} from '../{{pascalCase  name}}';"
            }
          );
          break;
        default:
          actions.push(
            {
              // Create file
              type: 'add',
              templateFile: 'templates/page.hbs',
              skipIfExists: true,
              path: 'src/containers/{{path}}/{{pascalCase  name}}/index.jsx'
            },
            {
              // Add the central scss file if it does not exist
              type: 'add',
              templateFile: 'templates/styles.hbs',
              skipIfExists: true,
              path: 'src/containers/{{path}}/{{pascalCase  name}}/index.scss'
            },
            {
              // Add the class to the central scss file
              type: 'append',
              template: `\n.{{dashCase name}}__page {}`,
              path: 'src/containers/{{path}}/{{pascalCase  name}}/index.scss'
            },
            {
              // Add the appropriate test file is it does not exist
              type: 'add',
              templateFile: 'templates/test.hbs',
              skipIfExists: true,
              path: 'src/containers/{{path}}/{{pascalCase  name}}/index.spec.js'
            },
            {
              type: 'modify',
              path: 'src/containers/{{path}}/{{pascalCase  name}}/index.spec.js',
              pattern: /## replace import here ##/gi,
              template: "// import {{pascalCase name}} from './index';"
            }
          );
          break;
      }
      return actions;
    }
  });
};
