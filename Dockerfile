FROM node:18.16

ARG GITHUB_TOKEN
ARG PORT
ARG DATADOG_API_KEY
ARG DATADOG_SITE


ENV GITHUB_TOKEN=$GITHUB_TOKEN
ENV DATADOG_API_KEY=${DATADOG_API_KEY}
ENV DATADOG_SITE=${DATADOG_SITE}

WORKDIR /home/<USER>

COPY . .
RUN ls -al

RUN npm rebuild node-sass
RUN npm ci
RUN npm i -g serve
RUN npm run build

RUN npm install -g @datadog/datadog-ci
# RUN DATADOG_API_KEY=$DATADOG_API_KEY DATADOG_SITE=$DATADOG_SITE npx @datadog/datadog-ci sourcemaps upload ./dist/assets \
#   --service=internal-dashboard \
#   --release-version=1.0.0 \
#   --minified-path-prefix="https://business-admin.korapay.com/assets"

ENV APP_PORT=6100
EXPOSE $APP_PORT

# Change ownership to a non-root user
#RUN useradd -m appuser
#RUN chown -R appuser:appuser /home/<USER>
#USER appuser

# CMD ["sh", "-c", "serve -s dist -l ${APP_PORT}"]
