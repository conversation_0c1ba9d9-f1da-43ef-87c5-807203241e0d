// import React from 'react';
// import user from '@testing-library/user-event';
// import { render, cleanup } from '@testing-library/react';
// import { Provider } from 'react-redux' //kindly remove if your test doesn't use redux
// import { axe, toHaveNoViolations } from 'jest-axe';
//import MockIndex from '+mock/MockIndex';
//import MockIndexWithRoute from '+mock/MockIndexWithRoute';
//import { store } from '+store'; //kindly remove if your test doesn't use redux
## replace import here ##

// afterEach(cleanup);
// afterEach(() => jest.clearAllMocks());
// expect.extend(toHaveNoViolations);

//Use this if your implementation requires redux but doesn't require specific route
//const Mocked{{pascalCase name}} = () => {
  // return (
    //<Provider store={store}>
   //   <MockIndex>
   //     <{{pascalCase name}} />
    //  </MockIndex>
    //</Provider>
  //);
//};

//Use this if your component doesn't require a specific route to work
//const Mocked{{pascalCase name}} = () => {
  // return (
   //   <MockIndex>
   //     <{{pascalCase name}} />
    //  </MockIndex>
  //);
//};

//Use this if your component needs to be rendered on a specific route
//const Mocked{{pascalCase name}} = () => {
  // return (
   //   <MockIndexWithRoute route={/** specific route **/} initialEntries={[/** All possible routes the component will navigate to during the test **/]}>
   //     <{{pascalCase name}} />
    //  </MockIndexWithRoute>
  //);
//};

//Use this if your implementation requires redux and also require a specific route
//const Mocked{{pascalCase name}} = () => {
  // return (
    //<Provider store={store}>
   //   <MockIndexWithRoute route={/** specific route **/} initialEntries={[/** All possible routes the component will navigate to during the test **/]}>
   //     <{{pascalCase name}} />
    //  </MockIndexWithRoute>
    //</Provider>
  //);
//};

//N.B: Kindly remove unused mocked component

// jest.mock( /** path to module being mocked **/, () => ({ /**  Insert data to be put **/}));

// describe('{{pascalCase name}}', () => {
//   // Default tests for all components/pages
//
//   test('Renders {{pascalCase name}}', () => {
//     const screen = render(<Mocked{{pascalCase name}} />);
//     expect(screen.getByText('An element on the screen')).toBeInTheDocument();
//   });

//   test('{{pascalCase name}} is accessible', async () => {
//      const screen = render(<Mocked{{pascalCase name}} />);
//      const results = await axe(screen.container);
//      expect(results).toHaveNoViolations();
//   });
// })


