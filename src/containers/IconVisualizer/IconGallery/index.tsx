import React from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import { iconNames } from '+containers/Dashboard/Shared/Icons/IconNames';

const IconGallery = () => (
  <div className="py-5" style={{ display: 'flex', flexWrap: 'wrap', gap: '32px', background: '#f5f5f5' }}>
    {iconNames.map(name => (
      <div key={name} style={{ width: '120px', textAlign: 'center', margin: '8px' }}>
        <Icon name={name} width={32} height={32} />
        <div style={{ fontSize: '12px', marginTop: '8px' }}>{name}</div>
      </div>
    ))}
  </div>
);

export default IconGallery;
