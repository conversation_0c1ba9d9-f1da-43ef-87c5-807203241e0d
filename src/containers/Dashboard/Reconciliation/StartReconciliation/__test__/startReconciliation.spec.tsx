import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it } from 'vitest';

import MockIndex from '+mock/MockIndex';
import useReconciliationStore from '+store/reconciliationStore';

import StartReconciliation from '../index';

const MockedStartReconciliation = () => {
  return (
    <MockIndex>
      <StartReconciliation />
    </MockIndex>
  );
};

describe('StartReconciliation', () => {
  beforeEach(() => {
    useReconciliationStore.setState({
      startReconciliationData: {
        processor: '',
        payment_type: 'payin',
        report_start_date: '',
        report_end_date: '',
        processor_file_id: '',
        processor_file_details: {
          key: ''
        },
        field_mapping: {
          processor: '',
          payment_type: 'payin',
          primary_key_mappings: [],
          comparison_key_mappings: []
        }
      },
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      autoMatchColumns: false
    });
  });

  it('should be accessible', async () => {
    const { container } = render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    });

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct content', () => {
    render(<MockedStartReconciliation />);

    expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    expect(screen.getByText('Fill the form below to start reconciliation process.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
  });

  it('should render form fields correctly', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Payment Type')).toBeInTheDocument();
    expect(screen.getByText('Report Date Range')).toBeInTheDocument();
    expect(screen.getByText('Upload record for reconciliation')).toBeInTheDocument();
  });

  it('should load processor dropdown', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });
  });

  it('should handle form interactions', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Pay-ins')).toBeInTheDocument();
  });

  it('should handle file upload', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);
    expect((fileInput as HTMLInputElement).files?.[0]).toBe(file);
  });

  it('should validate form completion', () => {
    render(<MockedStartReconciliation />);

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).toBeDisabled();
  });

  it('should submit reconciliation data to endpoint when form is completed', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });

    const processorSelect = screen.getByLabelText('Select Processor');
    await user.click(processorSelect);

    await waitFor(() => {
      const korapayOption = screen.getByText('Korapay');
      expect(korapayOption).toBeInTheDocument();
    });
    await user.click(screen.getByText('Korapay'));

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);
    const file = new File(['test,content\n1,data'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, file);

    const dateInput = screen.getByLabelText(/report date range/i);
    expect(dateInput).toBeInTheDocument();

    await user.click(dateInput);
    await user.clear(dateInput);
    await user.type(dateInput, '01/01/2024 - 31/01/2024');

    await waitFor(
      () => {
        const continueButton = screen.getByRole('button', { name: 'Continue' });
        expect(continueButton).not.toBeDisabled();
      },
      { timeout: 5000 }
    );

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    await user.click(continueButton);

    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    });
  });

  it('should have maxDate prop configured to prevent future date selection', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const dateInput = screen.getByLabelText(/report date range/i);
    expect(dateInput).toBeInTheDocument();

    await user.click(dateInput);

    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);
    const futureDateString = futureDate.toLocaleDateString('en-GB');

    await user.clear(dateInput);
    await user.type(dateInput, futureDateString);

    expect(dateInput).toHaveValue(futureDateString);
  });

  it('should accept past and current dates in date picker', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const dateInput = screen.getByLabelText(/report date range/i);
    expect(dateInput).toBeInTheDocument();

    const pastDate = new Date();
    pastDate.setDate(pastDate.getDate() - 7);
    const pastDateString = pastDate.toLocaleDateString('en-GB');

    await user.clear(dateInput);
    await user.type(dateInput, pastDateString);

    expect(dateInput).toHaveValue(pastDateString);
  });

  it('should show confirmation modal when trying to go back with form data', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    await waitFor(
      () => {
        expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
      },
      { timeout: 2000 }
    );

    const processorSelect = screen.getByLabelText('Select Processor');
    await user.click(processorSelect);

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, file);

    const goBackButton = screen.getByText('Go Back');
    await user.click(goBackButton);

    await waitFor(
      () => {
        const modalTitle = screen.queryByText('Go back?');
        expect(modalTitle).toBeInTheDocument();
      },
      { timeout: 2000 }
    );

    const confirmationMsg = screen.queryByText(/Please confirm that you want to cancel/i);
    expect(confirmationMsg).toBeInTheDocument();

    const cancelButton = screen.getByTestId('second-button');
    expect(cancelButton).toBeInTheDocument();

    await user.click(cancelButton);
  });

  it('should accept CSV file uploads', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);

    const csvFile = new File(['test,content'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, csvFile);
    expect((fileInput as HTMLInputElement).files?.[0]).toBe(csvFile);
  });

  it('should accept Excel file uploads', async () => {
    render(<MockedStartReconciliation />);

    const uploadRequirements = screen.getByText(/uploaded file must be in/i);
    expect(uploadRequirements).toBeInTheDocument();

    expect(uploadRequirements.textContent).toContain('XLSX');
  });

  it('should show appropriate info about file upload requirements', () => {
    render(<MockedStartReconciliation />);

    const uploadInfoText = screen.queryByText(/upload.*reconcile/i);
    expect(uploadInfoText).toBeInTheDocument();

    const allParagraphs = document.querySelectorAll('p');
    const uploadInfoParagraph = Array.from(allParagraphs).find(
      p => p.textContent?.includes('CSV') && p.textContent?.includes('XLSX') && p.textContent?.includes('10MB')
    );

    expect(uploadInfoParagraph).toBeTruthy();
  });

  it('should prevent upload of files larger than 10MB', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);

    const largeFile = new File([new ArrayBuffer(11 * 1024 * 1024)], 'large.csv', { type: 'text/csv' });
    await user.upload(fileInput, largeFile);

    await waitFor(
      () => {
        const errorMessage = screen.queryByText(/file size exceeds/i);
        expect(errorMessage).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
  });

  it('should handle file UI state correctly when returning with data', async () => {

    useReconciliationStore.setState({
      startReconciliationData: {
        processor: 'korapay',
        payment_type: 'payout',
        report_start_date: '2024-01-01',
        report_end_date: '2024-01-31',
        processor_file_id: '12345',
        processor_file_details: {
          key: 'test-key'
        },
        field_mapping: {
          processor: 'korapay',
          payment_type: 'payout',
          primary_key_mappings: [],
          comparison_key_mappings: []
        }
      },
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      autoMatchColumns: false
    });

    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    });

    const fileSection = screen.getByText('Upload record for reconciliation');
    expect(fileSection).toBeInTheDocument();

    expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
  });

  it('should render with data from the reconciliation store', async () => {
    useReconciliationStore.setState({
      startReconciliationData: {
        processor: 'korapay',
        payment_type: 'payout',
        report_start_date: '2024-01-01',
        report_end_date: '2024-01-31',
        processor_file_id: '',
        processor_file_details: {
          key: ''
        },
        field_mapping: {
          processor: 'korapay',
          payment_type: 'payout',
          primary_key_mappings: [],
          comparison_key_mappings: []
        }
      },
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      autoMatchColumns: false
    });

    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    });
  });

  it('should initialize store with correct data types', () => {
    useReconciliationStore.setState({
      startReconciliationData: {
        processor: 'test-processor',
        payment_type: 'payout',
        report_start_date: '2024-01-01',
        report_end_date: '2024-01-31',
        processor_file_id: '12345',
        processor_file_details: {
          key: 'test-key'
        },
        field_mapping: {
          processor: 'test-processor',
          payment_type: 'payout',
          primary_key_mappings: [],
          comparison_key_mappings: []
        }
      }
    });

    const state = useReconciliationStore.getState();
    expect(state.startReconciliationData.processor).toBe('test-processor');
    expect(state.startReconciliationData.payment_type).toBe('payout');
  });
});
