import React from 'react';

import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceHolder';
import EmptyStateComponent from '+shared/EmptyState';
import Modal from '+shared/Modal';
import Switch from '+shared/Switch';
import { ReconcileColumnSectionType } from '+types';

import ReconciliationOptionRow from '../components/ReconciliationOptionRow';
import { buildReconciliationReportOptions } from '../helpers/reconcileReportHelper';

const ReconcileColumnSection = ({
  comparisonKeyMappings,
  primaryKeyMappings,
  handleComparisonOptionChange,
  handleDelete,
  handleAddNewColumn,
  handleAutoMatchColumns,
  autoMatchColumns,
  removingItems,
  handleCancel,
  handleStartReconciliation,
  disableStartReconciliationButton,
  createReconciliation,
  displayPreview,
  handlePreviewDisplay,
  processorReportOptions,
  isLoading
}: ReconcileColumnSectionType) => {
  return (
    <>
      {isLoading ? (
        <div>
          <LoadingPlaceholder type="text" content={3} />
          <LoadingPlaceholder type="text" content={3} />
        </div>
      ) : (
        <>
          {comparisonKeyMappings.length === 0 ? (
            <section className="recon-report__content--empty">
              <EmptyStateComponent heading="No reconciliation columns" message="Add a column to start reconciliation" />
            </section>
          ) : (
            <>
              <div className="recon-report__content--field">
                <div className="recon-report__content--field__left">
                  <p className="recon-report__content--field__left--title">Columns detected on uploaded report</p>
                </div>
                <div className="recon-report__content--field__right">
                  <p className="recon-report__content--field__right--title">Column to be reconciled on Kora</p>
                </div>
              </div>
              <section className="recon-report__content--option">
                {comparisonKeyMappings.length > 0 &&
                  comparisonKeyMappings.map(item => {
                    const comparisonOptionWithoutCurrent = comparisonKeyMappings.filter(option => option.id !== item.id);
                    const isRemoving = removingItems.has(item.id);

                    return (
                      <div key={item.id} className={`reconciliation-option-row-wrapper ${isRemoving ? 'removing' : ''}`}>
                        <ReconciliationOptionRow
                          options={buildReconciliationReportOptions(primaryKeyMappings, comparisonOptionWithoutCurrent)}
                          value={item}
                          onChange={(value, field) => handleComparisonOptionChange(value, field, item.id)}
                          onDelete={() => handleDelete(item.id)}
                        />
                      </div>
                    );
                  })}
              </section>
            </>
          )}
          <button type="button" className="btn btn-light-blue border-dotted" onClick={handleAddNewColumn}>
            <i className="os-icon os-icon-plus mr-2 font-weight-bolder" />
            Add new column
          </button>
          <section className="recon-report__content--action">
            <div className="recon-report__content--action__left">
              <Switch checked={autoMatchColumns} onCheckedChange={handleAutoMatchColumns} />
              <p>Match columns automatically</p>
            </div>
            <div className="recon-report__content--action__right">
              <button type="button" className="cancel-button" onClick={handleCancel}>
                Cancel
              </button>
              <button
                type="button"
                className="btn btn-primary"
                onClick={handleStartReconciliation}
                disabled={disableStartReconciliationButton()}
              >
                {createReconciliation.isLoading ? (
                  <span className="spinner-border spinner-border-sm" aria-hidden="true" />
                ) : (
                  <>
                    Start Reconciliation
                    <i className="os-icon os-icon-arrow-right7 ml-2 font-weight-bolder" />
                  </>
                )}
              </button>
            </div>
          </section>

          <Modal
            visible={displayPreview}
            description={
              <div className="recon-report__preview">
                {processorReportOptions.map((item, index) => (
                  <span
                    key={item}
                    className="recon-report__preview--item"
                    style={{
                      backgroundColor: primaryKeyMappings[index]?.color
                    }}
                  >
                    {item}
                  </span>
                ))}
              </div>
            }
            heading="Processor Report Columns"
            close={() => handlePreviewDisplay()}
            firstButtonText="Close"
            firstButtonAction={() => handlePreviewDisplay()}
            hideSecondButton
            size="md"
            secondButtonColor="red"
          />
        </>
      )}
    </>
  );
};

export default ReconcileColumnSection;
