import React from 'react';

import Icon from '+shared/Icons';
import ReactSelectDropdown from '+shared/ReactSelectDropdown';
import { ReconciliationOptionRowType } from '+types';

const ReconciliationOptionRow = ({ options, value, onChange, onDelete }: ReconciliationOptionRowType) => {
  return (
    <div className="reconciliation-option-row">
      <div className="option-group">
        <div className="option-group__left">
          <ReactSelectDropdown
            options={options[0]}
            placeholder={`Select option`}
            label=""
            value={value.processor_report}
            onChange={value => onChange(value, 'processor_report')}
          />
        </div>
        <span>- Match with -</span>
        <div className="option-group__right">
          <ReactSelectDropdown
            options={options[1]}
            placeholder={`Select option`}
            label=""
            value={value.internal_report}
            onChange={value => onChange(value, 'internal_report')}
          />
        </div>
        <div
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              onDelete();
            }
          }}
          tabIndex={0}
          onClick={onDelete}
          role="button"
          aria-label="Delete mapping"
        >
          <Icon name="trashIcon" />
        </div>
      </div>
    </div>
  );
};

export default ReconciliationOptionRow;
