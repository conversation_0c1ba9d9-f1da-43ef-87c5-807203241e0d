import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { ComparisonKeyMappingType, PrimaryKeyMappingType, ReconcileColumnSectionType } from '+types';

import ReconcileColumnSection from '../ReconcileColumnSection';

describe('ReconcileColumnSection', () => {
  const mockPrimaryKeyMappings: PrimaryKeyMappingType = [
    {
      processor_report: 'transaction_id',
      internal_report: 'reference',
      color: '#FF5733'
    },
    {
      processor_report: 'amount',
      internal_report: 'payment_amount',
      color: '#33FF57'
    }
  ];

  const mockComparisonKeyMappings: ComparisonKeyMappingType = [
    {
      id: 'mapping-1',
      processor_report: 'transaction_id',
      internal_report: 'reference'
    },
    {
      id: 'mapping-2',
      processor_report: 'amount',
      internal_report: 'payment_amount'
    }
  ];

  const mockProcessorReportOptions = ['transaction_id', 'amount', 'date', 'status'];

  const defaultProps = {
    comparisonKeyMappings: mockComparisonKeyMappings,
    primaryKeyMappings: mockPrimaryKeyMappings,
    handleComparisonOptionChange: vi.fn(),
    handleDelete: vi.fn(),
    handleAddNewColumn: vi.fn(),
    handleAutoMatchColumns: vi.fn(),
    autoMatchColumns: false,
    removingItems: new Set<string>(),
    handleCancel: vi.fn(),
    handleStartReconciliation: vi.fn(),
    disableStartReconciliationButton: vi.fn(() => false),
    createReconciliation: { isLoading: false },
    displayPreview: false,
    handlePreviewDisplay: vi.fn(),
    processorReportOptions: mockProcessorReportOptions,
    isLoading: false
  };

  const MockedReconcileColumnSection = (props: Partial<ReconcileColumnSectionType>) => (
    <MockIndex>
      <ReconcileColumnSection {...defaultProps} {...props} />
    </MockIndex>
  );

  describe('ReconcileColumnSection', () => {
    test('should be accessible', async () => {
      const { container } = render(<MockedReconcileColumnSection />);
      const results = await axe(container, {
        rules: {
          label: { enabled: false }
        }
      });
      expect(results).toHaveNoViolations();
    });

    test('renders component with comparison key mappings', () => {
      render(<MockedReconcileColumnSection />);

      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
      expect(screen.getByText('Column to be reconciled on Kora')).toBeInTheDocument();
      expect(screen.getByText('Add new column')).toBeInTheDocument();

      const dropdowns = screen.getAllByRole('combobox');
      expect(dropdowns.length).toBeGreaterThanOrEqual(4);
    });

    test('renders empty state when no comparison key mappings', () => {
      render(<MockedReconcileColumnSection comparisonKeyMappings={[]} />);

      expect(screen.getByText('No reconciliation columns')).toBeInTheDocument();
      expect(screen.getByText('Add a column to start reconciliation')).toBeInTheDocument();
      expect(screen.queryByText('Columns detected on uploaded report')).not.toBeInTheDocument();
    });

    test('displays switch for auto match columns', () => {
      render(<MockedReconcileColumnSection />);

      expect(screen.getByText('Match columns automatically')).toBeInTheDocument();
      const switchElement = screen.getByRole('switch');
      expect(switchElement).toBeInTheDocument();
      expect(switchElement).not.toBeChecked();
    });

    test('displays checked switch when autoMatchColumns is true', () => {
      render(<MockedReconcileColumnSection autoMatchColumns={true} />);

      const switchElement = screen.getByRole('switch');
      expect(switchElement).toBeChecked();
    });

    test('calls handleAutoMatchColumns when switch is toggled', async () => {
      const user = userEvent.setup();
      const mockHandleAutoMatchColumns = vi.fn();

      render(<MockedReconcileColumnSection handleAutoMatchColumns={mockHandleAutoMatchColumns} />);

      const switchElement = screen.getByRole('switch');
      await user.click(switchElement);

      expect(mockHandleAutoMatchColumns).toHaveBeenCalledWith(true);
    });

    test('calls handleAddNewColumn when add new column button is clicked', async () => {
      const user = userEvent.setup();
      const mockHandleAddNewColumn = vi.fn();

      render(<MockedReconcileColumnSection handleAddNewColumn={mockHandleAddNewColumn} />);

      const addButton = screen.getByText('Add new column');
      await user.click(addButton);

      expect(mockHandleAddNewColumn).toHaveBeenCalledTimes(1);
    });

    test('calls handleCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      const mockHandleCancel = vi.fn();

      render(<MockedReconcileColumnSection handleCancel={mockHandleCancel} />);

      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      expect(mockHandleCancel).toHaveBeenCalledTimes(1);
    });

    test('calls handleStartReconciliation when start reconciliation button is clicked', async () => {
      const user = userEvent.setup();
      const mockHandleStartReconciliation = vi.fn();

      render(<MockedReconcileColumnSection handleStartReconciliation={mockHandleStartReconciliation} />);

      const startButton = screen.getByText('Start Reconciliation');
      await user.click(startButton);

      expect(mockHandleStartReconciliation).toHaveBeenCalledTimes(1);
    });

    test('disables start reconciliation button when disableStartReconciliationButton returns true', () => {
      const mockDisableStartReconciliationButton = vi.fn(() => true);

      render(<MockedReconcileColumnSection disableStartReconciliationButton={mockDisableStartReconciliationButton} />);

      const startButton = screen.getByText('Start Reconciliation');
      expect(startButton).toBeDisabled();
    });

    test('shows loading spinner when createReconciliation is loading', () => {
      render(<MockedReconcileColumnSection createReconciliation={{ isLoading: true }} />);

      expect(screen.queryByText('Start Reconciliation')).not.toBeInTheDocument();

      const buttons = screen.getAllByRole('button');
      const startButton = buttons.find(button => button.textContent?.includes('Start'));
      expect(startButton).toBeUndefined();
    });

    test('applies removing class when item is in removingItems set', () => {
      const removingItems = new Set(['mapping-1']);

      render(<MockedReconcileColumnSection removingItems={removingItems} />);

      const dropdowns = screen.getAllByRole('combobox');
      expect(dropdowns.length).toBeGreaterThanOrEqual(4);

      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
      expect(screen.getByText('Column to be reconciled on Kora')).toBeInTheDocument();
    });

    test('calls handleComparisonOptionChange when option is changed', async () => {
      const user = userEvent.setup();
      const mockHandleComparisonOptionChange = vi.fn();

      render(<MockedReconcileColumnSection handleComparisonOptionChange={mockHandleComparisonOptionChange} />);

      const dropdowns = screen.getAllByRole('combobox');
      expect(dropdowns.length).toBeGreaterThan(0);

      await user.click(dropdowns[0]);

      const options = screen.getAllByRole('option');
      expect(options.length).toBeGreaterThan(0);

      await user.click(options[0]);
      expect(mockHandleComparisonOptionChange).toHaveBeenCalled();
    });

    test('calls handleDelete when delete button is clicked', async () => {
      const user = userEvent.setup();
      const mockHandleDelete = vi.fn();

      render(<MockedReconcileColumnSection handleDelete={mockHandleDelete} />);

      const deleteButtons = screen.getAllByLabelText('Delete mapping');
      await user.click(deleteButtons[0]);

      expect(mockHandleDelete).toHaveBeenCalledWith('mapping-1');
    });

    test('does not display preview modal when displayPreview is false', () => {
      render(<MockedReconcileColumnSection displayPreview={false} />);

      expect(screen.queryByText('Processor Report Columns')).not.toBeInTheDocument();
    });

    test('displays preview modal when displayPreview is true', () => {
      render(<MockedReconcileColumnSection displayPreview={true} />);

      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
      expect(screen.getByText('Close')).toBeInTheDocument();
    });

    test('renders processor report options in preview modal', () => {
      render(<MockedReconcileColumnSection displayPreview={true} />);

      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();

      mockProcessorReportOptions.forEach(option => {
        expect(screen.getByText(option)).toBeInTheDocument();
      });
    });

    test('calls handlePreviewDisplay when modal close button is clicked', async () => {
      const user = userEvent.setup();
      const mockHandlePreviewDisplay = vi.fn();

      render(<MockedReconcileColumnSection displayPreview={true} handlePreviewDisplay={mockHandlePreviewDisplay} />);

      const closeButton = screen.getByTestId('close-button');
      await user.click(closeButton);

      expect(mockHandlePreviewDisplay).toHaveBeenCalledTimes(1);
    });

    test('calls handlePreviewDisplay when modal footer close button is clicked', async () => {
      const user = userEvent.setup();
      const mockHandlePreviewDisplay = vi.fn();

      render(<MockedReconcileColumnSection displayPreview={true} handlePreviewDisplay={mockHandlePreviewDisplay} />);

      const closeButtons = screen.getAllByText('Close');
      expect(closeButtons.length).toBeGreaterThan(0);

      await user.click(closeButtons[closeButtons.length - 1]);
      expect(mockHandlePreviewDisplay).toHaveBeenCalledTimes(1);
    });

    test('displays preview modal with processor report options', () => {
      render(<MockedReconcileColumnSection displayPreview={true} />);

      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();

      mockProcessorReportOptions.forEach(option => {
        expect(screen.getByText(option)).toBeInTheDocument();
      });

      expect(screen.getByText('Close')).toBeInTheDocument();
    });

    test('renders correct number of reconciliation option rows', () => {
      render(<MockedReconcileColumnSection />);

      const dropdowns = screen.getAllByRole('combobox');
      expect(dropdowns.length).toBe(mockComparisonKeyMappings.length * 2);
    });

    test('renders each mapping with proper form controls', () => {
      render(<MockedReconcileColumnSection />);

      const dropdowns = screen.getAllByRole('combobox');
      expect(dropdowns.length).toBe(4);

      const deleteButtons = screen.getAllByLabelText('Delete mapping');
      expect(deleteButtons).toHaveLength(2);
    });

    test('displays processor and internal report dropdowns for each mapping', () => {
      render(<MockedReconcileColumnSection />);

      const dropdowns = screen.getAllByRole('combobox');
      expect(dropdowns.length).toBeGreaterThanOrEqual(4);

      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
      expect(screen.getByText('Column to be reconciled on Kora')).toBeInTheDocument();
    });

    test('calls handleStartReconciliation when start button is clicked with modified mappings', async () => {
      const user = userEvent.setup();
      const mockHandleStartReconciliation = vi.fn();

      // Setup component with modified comparison key mappings
      const updatedMappings = [...mockComparisonKeyMappings];
      updatedMappings[0] = {
        ...updatedMappings[0],
        processor_report: 'date',
        internal_report: 'payment_amount'
      };

      render(
        <MockedReconcileColumnSection comparisonKeyMappings={updatedMappings} handleStartReconciliation={mockHandleStartReconciliation} />
      );

      const startButton = screen.getByText('Start Reconciliation');
      await user.click(startButton);

      // Verify the handler was called when button was clicked
      expect(mockHandleStartReconciliation).toHaveBeenCalledTimes(1);
    });
  });
});
