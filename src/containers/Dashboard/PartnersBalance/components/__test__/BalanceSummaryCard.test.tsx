import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { afterEach, describe, expect, it, Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import { mockedPartnerAccountBalance } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { CurrencyType, IPartnerAccount } from '+types';

import BalanceSummaryCard from '../BalanceSummaryCard';

const MockedBalanceSummaryCard = ({
  currency,
  showDetails = true,
  balance
}: {
  currency: CurrencyType;
  showDetails?: boolean;
  balance: IPartnerAccount;
}) => (
  <MockIndex>
    <BalanceSummaryCard currency={currency} showDetails={showDetails} balance={balance} />
  </MockIndex>
);

const mockDate = new Date(2024, 8, 10);
vi.setSystemTime(mockDate);

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

afterEach(() => {
  vi.useRealTimers();
});

describe('BalanceSummaryCard', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;

  it('renders BalanceSummaryCard component', async () => {
    render(<MockedBalanceSummaryCard currency={'NGN' as CurrencyType} balance={mockedPartnerAccountBalance as IPartnerAccount} />);

    await waitFor(() => expect(screen.getByTestId('bank-name')).toHaveTextContent('Monnify Test Bank - Monnify (**********)'));
    await waitFor(() => expect(screen.getByText('4,936,913,162.96')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByTestId('balance-time')).toHaveTextContent('Balance as at 12:00 AM, 10 Sep 2024'));
    expect(screen.queryByText('See Details')).not.toBeInTheDocument();
  });

  it('displays see details when permission is available and show details is true', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'partner_funding.view': true });
    render(<MockedBalanceSummaryCard currency={'NGN' as CurrencyType} balance={mockedPartnerAccountBalance as IPartnerAccount} />);

    await waitFor(() => expect(screen.getByText('See Details')).toBeInTheDocument());
  });

  it('hide see details when permission is available and show details is false', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'partner_funding.view': true });
    render(
      <MockedBalanceSummaryCard
        currency={'NGN' as CurrencyType}
        balance={mockedPartnerAccountBalance as IPartnerAccount}
        showDetails={false}
      />
    );

    await waitFor(() => expect(screen.queryByText('See Details')).not.toBeInTheDocument());
  });
});
