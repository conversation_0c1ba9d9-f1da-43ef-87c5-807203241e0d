import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import selectEvent from 'react-select-event';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { mockedPartnersAccountBalances } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import usePartnerBalanceStore from '+store/partnerBalanceStore';
import { AddFundsModalType, IPartnerBalance } from '+types';

import AddFundsModal from '../AddFundsModal';

const MockedAddFundsModal = ({
  type,
  accountId,
  setter
}: {
  type: AddFundsModalType;
  accountId: string;
  setter: (value: AddFundsModalType) => void;
}) => (
  <MockIndex>
    <AddFundsModal type={type} accountId={accountId} setter={setter} />
  </MockIndex>
);

beforeEach(() => {
  usePartnerBalanceStore.setState({ partnerBalances: mockedPartnersAccountBalances as Record<number, IPartnerBalance> });
});

describe('AddFundsModal', () => {
  it('renders AddFundsModal component', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="3" setter={vi.fn()} />);
    expect(screen.getByText('Add funds to balance')).toBeInTheDocument();
    expect(screen.getByText('Enter the desired amount you want to add.')).toBeInTheDocument();
    expect(screen.getByText('Source of Funds')).toBeInTheDocument();
    expect(screen.getByText('Amount')).toBeInTheDocument();
    expect(screen.getByTestId('first-button')).toBeInTheDocument();
    expect(screen.getByTestId('second-button')).toBeInTheDocument();
    expect(screen.getByTestId('second-button')).toBeDisabled();
  });

  it('validates amount correctly when amount exceeds source account balance', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="3" setter={vi.fn()} />);

    const amountInput = screen.getByPlaceholderText('Enter amount');

    fireEvent.change(amountInput, { target: { value: '60000' } });
    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['AweMFB (Wema Bank)']);
    expect(await screen.findByText('AweMFB (Wema Bank)')).toBeInTheDocument();
    expect(await screen.findByText('Amount must be less than or equal to source balance.')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());
  });

  it('validates amount correctly when amount exceeds source max transfer amount', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="3" setter={vi.fn()} />);

    const amountInput = screen.getByPlaceholderText('Enter amount');

    fireEvent.change(amountInput, { target: { value: '*********' } });
    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['Optimus Test (Test Bank)']);
    expect(await screen.findByText('Optimus Test (Test Bank)')).toBeInTheDocument();
    expect(await screen.findByText('Amount is greater than transfer threshold of 500,000.00.')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());
  });

  it('validates amount correctly when amount exceeds destination max deposit amount', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="3" setter={vi.fn()} />);

    const amountInput = screen.getByPlaceholderText('Enter amount');

    fireEvent.change(amountInput, { target: { value: '********' } });
    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['Optimus Test (Test Bank)']);
    expect(await screen.findByText('Optimus Test (Test Bank)')).toBeInTheDocument();
    expect(await screen.findByText('Amount is greater than amount of 300,000.00 the destination can receive.')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());
  });

  it('validates amount correctly when amount exceeds max balance amount the destination can have', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="8" setter={vi.fn()} />);

    const amountInput = screen.getByPlaceholderText('Enter amount');

    fireEvent.change(amountInput, { target: { value: '************' } });
    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['Globus Test (Test Bank)']);
    expect(await screen.findByText('Globus Test (Test Bank)')).toBeInTheDocument();
    expect(
      await screen.findByText('Amount is greater than amount of 40,000,000,000.00 balance the destination can have.')
    ).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());
  });

  it('account that can not transfer funds should not be selectable and button disabled', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="8" setter={vi.fn()} />);

    const amountInput = screen.getByPlaceholderText('Enter amount');

    await userEvent.type(amountInput, '************');
    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['Korapay Test (Test Bank)']);
    await waitFor(() => expect(screen.getByTestId('Korapay Test')).toHaveClass('select-option disabled'));

    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());
  });

  it('Should proceed to next step when amount is valid ', async () => {
    render(<MockedAddFundsModal type="add-funds" accountId="3" setter={vi.fn(() => 'confirm')} />);

    const amountInput = screen.getByPlaceholderText('Enter amount');

    fireEvent.change(amountInput, { target: { value: '40000' } });
    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['Optimus Test (Test Bank)']);
    expect(await screen.findByText('Optimus Test (Test Bank)')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeEnabled());
    const nextButton = screen.getByTestId('second-button');
    await waitFor(() => expect(nextButton).toBeEnabled());
  });
});
