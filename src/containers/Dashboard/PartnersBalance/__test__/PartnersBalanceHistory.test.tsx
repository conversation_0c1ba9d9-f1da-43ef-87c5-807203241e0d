import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import selectEvent from 'react-select-event';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import { mockedPartnersAccountBalances } from '+mock/mockData';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { server } from '+mock/mockServers';
import usePartnerBalanceStore from '+store/partnerBalanceStore';
import { IPartnerBalance } from '+types';

import PartnersBalanceHistory from '../PartnersBalanceHistory';

const mockDate = new Date(2024, 8, 10);

vi.setSystemTime(mockDate);

afterAll(() => {
  vi.useRealTimers();
});

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockPartnerBalanceHistory = () => {
  return (
    <MockIndexWithRoute route="/dashboard/partners-balance/:accountId" initialEntries={['/dashboard/partners-balance/3']}>
      <PartnersBalanceHistory />
    </MockIndexWithRoute>
  );
};

beforeEach(() => {
  server.use(
    http.get('/admin/partner-funding/3/account', () => {
      return HttpResponse.json({ data: mockedPartnersAccountBalances['3'] }, { status: 200 });
    })
  );
  usePartnerBalanceStore.setState({ partnerBalances: mockedPartnersAccountBalances as Record<number, IPartnerBalance> });
});

describe('PartnersBalanceHistory', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({
    'partner_funding.create': true,
    'partner_funding_details.view': true,
    'partner_funding.export': true
  });
  it('PartnerBalanceHistory is accessible', async () => {
    const { container } = render(<MockPartnerBalanceHistory />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('should render account details', async () => {
    render(<MockPartnerBalanceHistory />);
    expect(screen.getByText('Transaction History')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Add Funds' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Refetch Balance' })).toBeInTheDocument();
    await waitFor(() => expect(screen.queryByTestId('bank-name')).toHaveTextContent('Monnify Test Bank - Monnify (**********)'));
    await screen.findByText('4,936,913,162.96');
    await waitFor(() => expect(screen.getByTestId('balance-time')).toHaveTextContent('Balance as at 12:00 AM, 10 Sep 2024'));
  });
  it('should render add funds button when permission is available', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'partner_funding.create': true });
    render(<MockPartnerBalanceHistory />);
    expect(await screen.findByText('Transaction History')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Add Funds' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Refetch Balance' })).toBeInTheDocument();
    await waitFor(() => expect(screen.queryByTestId('bank-name')).toHaveTextContent('Monnify Test Bank - Monnify (**********)'));
  });
  it('should display table data', async () => {
    render(<MockPartnerBalanceHistory />);
    expect(await screen.findByText('Transaction History')).toBeInTheDocument();
    await screen.findByText('All Transactions (5)');
    await waitFor(() => expect(screen.getAllByText('Success')).toHaveLength(6));
    await waitFor(() => expect(screen.getAllByTestId('amount')[0]).toHaveTextContent('-20,000.00'));
    await waitFor(() => expect(screen.getAllByTestId('amount')[1]).toHaveTextContent('-5,000.00'));
    await waitFor(() => expect(screen.getAllByTestId('amount')[2]).toHaveTextContent('-500,000.00'));
    await waitFor(() => expect(screen.getAllByTestId('amount')[3]).toHaveTextContent('-2,500.00'));
    await waitFor(() => expect(screen.getAllByTestId('amount')[4]).toHaveTextContent('-20,000.00'));
  });
  it('should display add fund modal when add funds button is clicked', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'partner_funding.create': true });
    render(<MockPartnerBalanceHistory />);
    expect(screen.getByText('Transaction History')).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: 'Add Funds' }));

    await screen.findByText('Add funds to balance');
    await screen.findByText('Enter the desired amount you want to add.');
    await screen.findByLabelText('Source of Funds');
    await screen.findByLabelText('Source of Funds');

    await waitFor(() => expect(screen.getByTestId('first-button')).toHaveTextContent('Cancel'));
    await waitFor(() => expect(screen.getByTestId('second-button')).toHaveTextContent('Next'));
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());
  });

  it('should perform a successful transfer', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'partner_funding.create': true });
    render(<MockPartnerBalanceHistory />);
    expect(screen.getByText('Transaction History')).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: 'Add Funds' }));

    await screen.findByText('Add funds to balance');
    await screen.findByText('Enter the desired amount you want to add.');
    await screen.findByLabelText('Source of Funds');

    await selectEvent.select(screen.getByLabelText('Source of Funds'), ['Optimus Test (Test Bank)']);
    expect(await screen.findByText('Optimus Test (Test Bank)')).toBeInTheDocument();
    const amountInput = screen.getByPlaceholderText('Enter amount');
    fireEvent.change(amountInput, { target: { value: '1000000' } });
    expect(screen.getByTestId('second-button')).toBeInTheDocument();

    await waitFor(() => expect(screen.getByTestId('second-button')).toBeEnabled());
    await userEvent.click(screen.getByTestId('second-button'));
    await waitFor(async () => expect(await screen.findByText('Confirm Transaction')).toBeInTheDocument());
    expect(
      await screen.findByText('Please review the transaction information and confirm that you want to proceed with this transaction.')
    ).toBeInTheDocument();
    await waitFor(() =>
      expect(screen.getByTestId('confirm-transaction-message')).toHaveTextContent(
        'You are about to add NGN 10,000.00 from Test Bank to Monnify Test Bank.'
      )
    );

    await waitFor(() => expect(screen.getByTestId('second-button')).toHaveTextContent('Yes, Confirm'));
    await userEvent.click(screen.getByTestId('second-button'));

    await screen.findByText('Transaction Successful');
    await screen.findByText('View payment details');
  });
});
