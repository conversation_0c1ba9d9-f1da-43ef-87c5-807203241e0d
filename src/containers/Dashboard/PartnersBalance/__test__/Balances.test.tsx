import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import { mockedPartnersAccountBalances } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import Balances from '../Balances';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedBalances = () => {
  return (
    <MockIndex>
      <Balances />
    </MockIndex>
  );
};

server.use(
  http.get('/admin/partner-funding/3/account', () => {
    return HttpResponse.json({ data: mockedPartnersAccountBalances['3'] }, { status: 200 });
  })
);

describe('Balances', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'processor_accounts.view': true });
  it('Balances is accessible', async () => {
    const { container } = render(<MockedBalances />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('should render accounts details', async () => {
    render(<MockedBalances />);
    await waitFor(() => expect(screen.getAllByText('Partner Balances')).toHaveLength(2));
    await waitFor(() => expect(screen.getAllByTestId('bank-name')).toHaveLength(4));

    await waitFor(() => expect(screen.getAllByTestId('bank-name')[0]).toHaveTextContent('Monnify Test Bank - Monnify (**********)'));

    await screen.findByText('5,000,000,000.00');
    await waitFor(() => expect(screen.getAllByTestId('bank-name')[1]).toHaveTextContent('Providus Bank - NATHAN AGBARA (**********)'));

    await waitFor(() => expect(screen.getAllByTestId('bank-name')[2]).toHaveTextContent('Wema Bank - AweMFB (**********)'));
    expect(screen.getAllByText(/0.00/)[0]).toBeInTheDocument();

    await screen.findByText('500.00');
    await waitFor(() => expect(screen.getAllByTestId('bank-name')[3]).toHaveTextContent('Test Bank - Korapay Test (**********)'));
  });
});
