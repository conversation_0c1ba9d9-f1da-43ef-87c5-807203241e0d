import React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import Payouts from '../index';

const MockedPayouts = () => {
  return (
    <MockIndex>
      <Payouts />
    </MockIndex>
  );
};

describe('Payouts', () => {
  test('Payouts is accessible', async () => {
    const { container } = render(<MockedPayouts />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
