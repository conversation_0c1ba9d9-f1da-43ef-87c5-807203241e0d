import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import { CurrencyType } from '+types';

import TransactionDetails from '..';
import testData from './testData';

const TrxnDetails = ({ children }: { children?: React.ReactNode }) => {
  return (
    <MockIndex>
      <TransactionDetails>
        <TransactionDetails.Header
          heading={testData.heading}
          currency={testData.currency}
          statusLabels={[
            {
              status: testData.status
            }
          ]}
          actionButtons={testData.actionButtons}
          summaries={testData.summaries}
          isLoading={testData.isLoading}
        />
        <TransactionDetails.Section heading="A section" isLoading={testData.isLoading} />
        {children}
      </TransactionDetails>
    </MockIndex>
  );
};

const renderWithoutDisputes = <TrxnDetails />;
const renderWithDisputes = (
  <TrxnDetails>
    <TransactionDetails.Section heading="Disputes" isLoading={testData.isLoading}>
      <TransactionDetails.Disputes
        tabs={['refunds', 'chargebacks']}
        disputesGenerators={testData.disputesGenerators}
        summaryGenerators={testData.summaryGenerators}
        childrenGenerators={testData.childrenGenerators}
        currency={testData.currency as CurrencyType}
      />
    </TransactionDetails.Section>
  </TrxnDetails>
);

const setup = (Component: React.ReactElement) => {
  const utils = render(Component);

  return {
    user: userEvent.setup(),
    ...utils
  };
};

describe('Simple Transaction details', () => {
  it('Renders', () => {
    setup(renderWithoutDisputes);
    expect(screen.getByText(testData.heading));
    expect(screen.getByText(testData.currency));
    expect(screen.getByText(new RegExp(testData.status, 'i')));
    expect(screen.getByRole('button', { name: testData.actionButtons[0].children }));
  });

  it('Is accessible', async () => {
    const { container } = setup(renderWithoutDisputes);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});

describe('Transaction details with Disputes section', () => {
  test('Renders', () => {
    setup(renderWithDisputes);
    // Tests for the visible section heading named 'Disputes'
    expect(screen.getAllByText(/disputes/i)[0]).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /refunds \(1\)/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /chargebacks \(1\)/i })).toBeInTheDocument();
  });

  test('Is accessible', async () => {
    // skipped because Copyable component is not accessible and is causing this test to fail
    const { container } = setup(renderWithDisputes);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Renders Chargeback Dispute correctly', async () => {
    const { user } = setup(renderWithDisputes);

    await user.click(screen.getByRole('button', { name: /chargebacks \(1\)/i }));

    expect(screen.getByText(/1 chargeback found/i)).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /chargeback of 100\.00 ngn/i })).toBeInTheDocument();

    expect(screen.getByText(/chargeback amount/i)).toBeInTheDocument();
    expect(screen.getAllByText(/100\.00 ngn/i)[0]).toBeInTheDocument();

    expect(screen.getByText(/escalated amount/i)).toBeInTheDocument();
    expect(screen.getAllByText(/100\.00 ngn/i)[1]).toBeInTheDocument();

    expect(screen.getByText(/accepted amount/i)).toBeInTheDocument();
    expect(screen.getAllByText(/100\.00 ngn/i)[2]).toBeInTheDocument();

    expect(screen.getByText(/chargeback status/i)).toBeInTheDocument();
    expect(screen.getByText(/chargeback accepted/i)).toBeInTheDocument();

    expect(screen.getByText(/date escalated/i)).toBeInTheDocument();
    expect(screen.getByText(/6 dec 2023 \| 8:52 am/i)).toBeInTheDocument();

    expect(screen.getByText(/date completed/i)).toBeInTheDocument();
    expect(screen.getByText(/6 dec 2023 \| 4:17 pm/i)).toBeInTheDocument();

    expect(screen.getByRole('link', { name: /view details/i })).toBeInTheDocument();
  });

  test('Renders Refund Dispute correctly', async () => {
    const { user } = setup(renderWithDisputes);

    await user.click(screen.getByRole('button', { name: /refunds \(1\)/i }));

    expect(screen.getByText(/1 refund found/i)).toBeInTheDocument();
    // expect(screen.getByRole('tab', { name: /refund of 100\.00 ngn/i })).toBeInTheDocument();

    expect(screen.getByText(/refund id/i)).toBeInTheDocument();
    expect(screen.getByText(/ref-refunds/i)).toBeInTheDocument();

    expect(screen.getByText(/refund amount/i)).toBeInTheDocument();
    expect(screen.getAllByText(/100\.00 ngn/i)[1]).toBeInTheDocument();

    expect(screen.getByText(/refund status/i)).toBeInTheDocument();
    expect(screen.getByText(/pending/i)).toBeInTheDocument();

    expect(screen.getByText(/date completed/i)).toBeInTheDocument();
    expect(screen.getByText('-- | --')).toBeInTheDocument();

    expect(screen.getByText(/reason for refund/i)).toBeInTheDocument();
    expect(screen.getByText(/not available/i)).toBeInTheDocument();
  });
});
