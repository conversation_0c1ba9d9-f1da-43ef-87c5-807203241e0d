import React from 'react';
import { Link } from 'react-router-dom';
import Copyable from '+containers/Dashboard/Shared/Copyable';
import { CurrencyType } from '+types';
import { chargebackStatusTagConfig, getDate, getTime, StatusType } from '+utils';

export default {
  heading: '1000',
  currency: 'NGN',
  status: 'accepted',
  actionButtons: [
    {
      children: 'Click me',
      onClick: () => {}
    }
  ],
  summaries: [
    { label: 'label 1', value: 'Label 1 Value' },
    { label: 'label 2', value: 'Label 2 Value' },
    { label: 'label 3', value: 'Label 3 Value' }
  ],
  isLoading: false,
  disputesGenerators: {
    refunds: [
      {
        reference: 'REF-refunds',
        amount: '100.00',
        status: 'pending',
        description: null,
        date_completed: null
      }
    ],
    chargebacks: [
      {
        reference: 'CHB-chargebacks',
        amount: '100.00',
        accepted_amount: '100.00',
        status: 'accepted',
        escalation_date: '2023-12-06T08:52:13.000Z',
        actual_resolution_date: '2023-12-06T16:17:43.000Z'
      }
    ]
  },
  summaryGenerators: {
    refunds: (item: any, currency: CurrencyType) => [
      { label: 'Refund ID', value: <Copyable text={item.reference} spanClassName="copyable-blue" /> },
      { label: 'Refund Amount', value: `${item.amount} ${currency}` },
      {
        label: 'Refund Status',
        value: (
          <span style={{ color: chargebackStatusTagConfig[item.status as StatusType].color ?? '' }}>
            {chargebackStatusTagConfig[item.status as StatusType].name}
          </span>
        )
      },
      {
        label: 'Date Created',
        value: `${getDate(item.created_at)} | ${getTime(item.created_at)}`,
        hidden: !item.created_at
      },
      {
        label: 'Date Completed',
        value: item.date_completed ? `${getDate(item.date_completed)} | ${getTime(item.date_completed)}` : '-- | --'
      },
      {
        label: 'Reason for Refund',
        value: (item?.reversal_reason || item.description) ?? 'Not Available'
      }
    ],
    chargebacks: (item: any, currency: CurrencyType) => [
      { label: 'Chargeback ID', value: <Copyable text={item.reference} spanClassName="copyable-blue" /> },
      { label: 'Chargeback Amount', value: `${item.amount} ${currency}` },
      { label: 'Escalated Amount', value: `${item.amount} ${currency}` },
      { label: 'Accepted Amount', value: `${item.accepted_amount} ${currency}` },
      {
        label: 'Chargeback Status',
        value: (
          <span style={{ color: chargebackStatusTagConfig[item.status as StatusType].color ?? '' }}>
            {chargebackStatusTagConfig[item.status as StatusType].name}
          </span>
        )
      },
      {
        label: 'Date Escalated',
        value: item.escalation_date ? `${getDate(item.escalation_date)} | ${getTime(item.escalation_date)}` : '-- | --'
      },
      {
        label: 'Date Completed',
        value: item.actual_resolution_date ? `${getDate(item.actual_resolution_date)} | ${getTime(item.actual_resolution_date)}` : '-- | --'
      }
    ]
  },
  childrenGenerators: {
    chargebacks: (item: any) => (
      <Link className="btn btn-primary" to={`/dashboard/issuing/issued-card-chargebacks/${item?.reference}`}>
        View Details
      </Link>
    )
  }
};
