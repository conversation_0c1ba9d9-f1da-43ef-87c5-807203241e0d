.dispute-sidebar {
  --sidebar-size: 20rem;
  --breakpoint: 65%;

  > * + * {
    margin-top: 0.5rem;
  }

  .counter {
    padding: 0.5rem 1rem;
    margin: 0;
    font-size: inherit;
  }

  .dispute-amount {
    padding: 1em;
    background-color: transparent;
    width: 100%;
    text-align: left;
    border: none;

    &:focus-visible {
      outline: auto;
    }

    &.active {
      color: #2376f3;
      background-color: #f1f6fa;
    }

    > em {
      font-weight: 500;
    }
  }
}

.dispute-not-sidebar {
  padding: 1rem;

  > * + * {
    margin-top: 0.5rem;
    border-top: 1.5px dashed #dde2ec;
  }
}

.dispute-summary-item + .dispute-summary-item {
  margin-top: 1rem;
}

.dispute-summary-label {
  color: #a9afbc;
  font-weight: 500;
  flex-grow: 1;
  max-width: 30ch;
  min-width: min(30%, 30ch);
}

.dispute-summary-value {
  flex-grow: 1;
  text-align: right;
  max-width: 70ch;

  @media (max-width: 768px) {
    text-align: right;
    min-width: 0;
    overflow-wrap: break-word;
  }
}

.dispute-children-container {
  padding-block: 1rem;

  .btn {
    font-size: inherit;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    border: none;
    width: 100%;
  }
}

.reversal-pg-route {
  all: unset;
  cursor: pointer;
  color: #007bff;
  display: block;
  font-weight: 500;
  margin: 0 0 1rem auto;
  letter-spacing: 0.2px;
  text-align: right;
}
