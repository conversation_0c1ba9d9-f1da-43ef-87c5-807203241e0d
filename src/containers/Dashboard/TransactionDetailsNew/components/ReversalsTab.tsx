/* eslint-disable no-unused-expressions */
/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import { CurrencyType, SummaryItemType } from '+types';
import { formatAmount } from '+utils';

import NoDisputesFound from './NoDisputesFound';

import './index.scss';

const ReversalsTab = <T extends { reference: string; amount: string }>({
  data,
  currency,
  summaryGenerator,
  childrenGenerator,
  actionGenerator
}: {
  data: Array<T>;
  currency: CurrencyType;
  summaryGenerator: (dataItem: T, ...args: Array<any>) => Array<SummaryItemType>;
  childrenGenerator: (dataItem: T, ...args: Array<any>) => React.ReactNode;
  actionGenerator?: (dataItem: T, ...args: Array<any>) => React.ReactNode;
}) => {
  if (!data || data?.length === 0) return <NoDisputesFound description=" No reversals found for this transaction" />;

  const [activeIndex, setActiveIndex] = useState(0);
  const reversalsCount = data?.length;
  const summaries = summaryGenerator(data[activeIndex], currency);
  const children = childrenGenerator?.(data[activeIndex]);

  const reversals = (
    <ul>
      {data.map((reversal, index) => (
        <li key={reversal.reference}>
          <button
            onClick={() => setActiveIndex(index)}
            type="button"
            className={`dispute-amount ${activeIndex === index ? 'active' : ''}`}
            aria-selected={activeIndex === index}
            id={`${index}-controller`}
            role="tab"
            aria-controls={`${index}-panel`}
          >
            Reversal of <strong>{`${formatAmount(reversal.amount)} ${currency}`} </strong>
          </button>
        </li>
      ))}
      {actionGenerator?.(data[activeIndex], data[activeIndex].reference)}
    </ul>
  );

  return (
    <section className="with-sidebar">
      <div className="sidebar dispute-sidebar">
        <p className="counter">{`${reversalsCount} ${reversalsCount > 1 ? 'Reversals' : 'Reversal'} found`}</p> {reversals}
      </div>

      <div
        className="not-sidebar dispute-not-sidebar"
        aria-labelledby={`${activeIndex}-controller`}
        id={`${activeIndex}-panel`}
        role="tabpanel"
      >
        <ul className="dispute-summaries">
          <Link className="reversal-pg-route" to={`/dashboard/payment-reversals/${data[0]?.reference}`}>
            View Reversal Details
          </Link>
          {summaries.map((summary, index) => {
            return summary.hidden ? null : (
              <li className="dispute-summary-item cluster nowrap" key={summary.label as string}>
                <div className="dispute-summary-label" id={`${index}`}>
                  {summary.label}
                </div>
                <div className="dispute-summary-value" aria-labelledby={`${index}`}>
                  {summary.value}
                </div>
              </li>
            );
          })}
        </ul>

        <div className="dispute-children-container">{children}</div>
      </div>
    </section>
  );
};

export default ReversalsTab;
