import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import TransactionDetails from '../index';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedTransactionDetails = () => {
  return (
    <MockIndexWithRoute route="/dashboard/pay-ins/:id" initialEntries={['/dashboard/pay-ins/6KsdgnKp65pK']}>
      <TransactionDetails />
    </MockIndexWithRoute>
  );
};

describe('TransactionDetails', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'pay_in_details.view': true });
  test('TransactionDetails is accessible', async () => {
    const { container } = render(<MockedTransactionDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('Renders TransactionDetails', async () => {
    render(<MockedTransactionDetails />);
    await waitFor(() => expect(screen.getAllByText('Account Name')).toHaveLength(2));
    await screen.findByText('Testing Test');
    await screen.findByText('Account Number');
    await screen.findByText('**********');
    await screen.findByText('Email');
    await screen.findByText('<EMAIL>');
    await screen.findByText('Payment Method');
    await screen.findByText('Bank Transfer');

    await screen.findByText('Wema');
    await screen.findByText('**********');
    await waitFor(() => expect(screen.getAllByText('1,000.00 NGN')).toHaveLength(3));
    await screen.findByText('Success');
    await screen.findByText("Customer's Virtual Account");
    await screen.findByText('No refunds found for this transaction');
    await screen.findByText('Refunds, Reversals & Chargebacks');
    await screen.findByText('Chargebacks');
    await screen.findByText('Refunds');
    await screen.findByText('Reversals');
  });
});
