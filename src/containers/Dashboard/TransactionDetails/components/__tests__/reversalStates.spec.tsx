import React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { IReversalStatesProps } from '+types';

import ReversalStates from '../ReversalStates';

const MockedReversalsStates = ({ reversalType, reversalState, refreshFn }: IReversalStatesProps) => {
  return (
    <MockIndex>
      <ReversalStates reversalType={reversalType} reversalState={reversalState} refreshFn={refreshFn} />
    </MockIndex>
  );
};
describe('Reversal States', () => {
  test('ReversalsInfoModal is accessible', async () => {
    const { container } = render(<MockedReversalsStates reversalType="type" reversalState="loading" refreshFn={vi.fn()} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test("should render LoadingReversalState when reversalState is 'loading'", () => {
    const { getByText } = render(<MockedReversalsStates reversalType="type" reversalState="loading" refreshFn={vi.fn()} />);
    expect(getByText('Please wait...')).toBeInTheDocument();
  });

  test("should render ErrorReversalState when reversalState is 'error'", () => {
    const { getByText } = render(<MockedReversalsStates reversalType="type" reversalState="error" refreshFn={vi.fn()} />);
    expect(getByText('Something went wrong!')).toBeInTheDocument();
  });
});
