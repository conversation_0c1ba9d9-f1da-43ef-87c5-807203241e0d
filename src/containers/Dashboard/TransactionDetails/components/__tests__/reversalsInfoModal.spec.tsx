import React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import ReversalsInfoModal, { IReversalsInfoModalProps } from '../ReversalsInfoModal';

const MockedReversalsInfoModal = ({ visible, close }: IReversalsInfoModalProps) => {
  return (
    <MockIndex>
      <ReversalsInfoModal visible={visible} close={close} />
    </MockIndex>
  );
};

describe('ReversalsInfoModal', () => {
  test('ReversalsInfoModal is accessible', async () => {
    const { container } = render(<MockedReversalsInfoModal visible close={vi.fn()} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
