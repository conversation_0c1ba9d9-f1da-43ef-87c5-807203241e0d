import React, { useLayoutEffect, useState } from 'react';
import Copyable from '+shared/Copyable';
import { formatAmount } from '+utils';
import pendingCircles from '+assets/img/dashboard/pending-circles.gif';
import ReversalStates from '../ReversalStates';
import '../index.scss';

const Chargebacks = ({ data, currency, isLoading, isError, refetch }) => {
  const items = [];
  const [item, setItem] = useState(items);
  const count = data?.length;

  const [activeReference, setActiveReference] = useState(null);

  const handleClick = (item) => {
    setItem([item]);
    setActiveReference(item.reference);
  };

  const switchRefundStatus = {
    success: { name: 'Successful', color: '#24B314', icon: '✓' },
    manual: { name: 'Manual', color: '#24B314', icon: '✓' },
    pending: {
      name: 'Processing...',
      color: 'rgba(16,38,73,.4)',
      icon: <img src={pendingCircles} alt="" id="pending-circles" />
    },
    processing: {
      name: 'Processing...',
      color: 'rgba(16,38,73,.4)',
      icon: <img src={pendingCircles} alt="" id="pending-circles" />
    },
    failed: { name: 'Failed', color: '#F32345', icon: '✗' }
  };

  useLayoutEffect(() => {
    if (data?.length > 0) {
      setItem([data[0]]);
      setActiveReference(data[0].reference);
    }
  }, [data]);

  return (
    <div>
      {isLoading && <ReversalStates reversalState="loading" reversalType="chargebacks" />}
      {isError && <ReversalStates reversalState="error" reversalType="chargebacks" refreshFn={() => refetch()} />}
      {data?.length > 0 && (
        <ul>
          <li id="payment-detail">
            <div style={{ width: '40%' }}>
              <p className="reversal-count">{`${count} ${count > 1 ? 'Chargebacks' : 'Chargeback'} found`}</p>
              {data.map((item) => {
                if (item) {
                  items.push(item);
                }
                return (
                  <div 
                  className={`--container ${activeReference === item.reference ? 'active-dispute' : ''}`}
                    key={item.reference}
                    onClick={() => handleClick(item)}
                  >
                    <p className="reversal-amount">
                      <label>Chargeback of</label>
                      {`${formatAmount(item.amount || 0)} ${currency}`}
                    </p>
                    <span className={`tab-icon ${activeReference === item.reference && 'active'}`} />
                  </div>
                );
              })}
            </div>
            <div className="customer-reversal-info" style={{ width: '60%' }}>
              <ul className="">
                <li>
                  <p>Reason for Chargeback</p>
                  <p>Chargeback</p>
                </li>
                <li>
                  <p>Chargeback Destination</p>
                  <p>{item.destination === 'disbursement_wallet' ? `Balance` : 'Customer'}</p>
                </li>
                <li>
                  <p>Chargeback Status</p>
                  <p
                    style={{
                      color: switchRefundStatus[item[0]?.status]?.color || ''
                    }}
                  >
                    {switchRefundStatus[item[0]?.status]?.name}
                  </p>
                </li>
                <li>
                  <p>Reference</p>
                  <Copyable text={item[0]?.reference} spanClassName="reversal_ref" />
                </li>
              </ul>
            </div>
          </li>
        </ul>
      )}
      {data?.length === 0 && !isLoading && !isError && <ReversalStates reversalState="empty" reversalType="chargebacks" />}
    </div>
  );
};
export default Chargebacks;
