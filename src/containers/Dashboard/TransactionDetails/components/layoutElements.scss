@import 'styles/kpy-custom/variables';

.tx-layout-heading {
  margin-bottom: 15px;
  .main-row {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    border-bottom: 0.5px solid #a9afbc;
    column-gap: 20px;

    @media (min-width: $breakpoint-tablet) {
      justify-content: space-between;
    }

    .text-row {
      display: flex;
      align-items: flex-end;
      flex-wrap: wrap;
      margin-bottom: 10px;
      .heading {
        color: #292b2c;
        font-size: 1.4rem;
        font-weight: 600;
        margin: 0;
        margin-right: 10px;
        line-height: 100%;

        .subscript {
          font-size: 1rem;
          margin-left: 5px;
          color: #a9afbc;
        }

        @media (min-width: $breakpoint-tablet) {
          margin-right: 20px;
          font-size: 1.5rem;
        }
      }
      .tag {
        font-weight: 500;
        border-radius: 5px;
        padding: 3px 7px;
        font-size: 0.75rem;

        @media (min-width: $breakpoint-tablet) {
          font-size: 0.85rem;
          padding: 4px 10px;
        }
      }
    }

    .action-buttons {
      display: flex;
      flex-direction: row;
      column-gap: 10px;
    }
    .actions-row {
      margin-bottom: 10px;
    }
    .action-btn {
      display: flex;
      align-items: center;
      gap: 1em;
    }
  }
}

.tx-summary-info {
  margin-bottom: 20px;
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    row-gap: 5px;
    p {
      margin-bottom: 0;
    }
    .label {
      font-weight: 400;
      font-size: 0.9rem;
      color: #414f5f;
      display: flex;
      align-items: center;
      margin-bottom: 0px;

      @media (min-width: $breakpoint-tablet) {
        margin-bottom: 5px;
      }
    }
    .value {
      font-weight: 600;
      font-size: 0.9rem;
      justify-content: center;
      color: #3e4b5b;
    }
    .content-after {
      display: flex;
      align-items: center;
      text-decoration: none;
      .icon-after {
        font-size: 1.1rem;
        font-weight: 600;
        margin-left: 3px;
      }
    }
    .copy-button {
      img {
        width: 0.9rem;
      }
    }

    @media (min-width: $breakpoint-tablet) {
      width: fit-content;
      display: inline-block;

      &:not(:first-child) {
        border-left: 1px solid #dde2ec;
        padding-left: 0.5rem;
        margin-left: 10px;
      }
    }

    @media (min-width: $breakpoint-desktop) {
      &:not(:first-child) {
        padding-left: 0.8rem;
      }
    }
  }

  @media (min-width: $breakpoint-tablet) {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
}

.tx-section-header {
  border-bottom: 0.5px solid #a9afbc;
  padding-bottom: 5px;
  column-gap: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
  display: flex;
  p {
    margin: 0;
  }

  .heading {
    color: #414f5f;
    font-weight: 600;
    font-size: 1rem;
  }
}

.tx-info-list {
  margin-bottom: 40px;
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    row-gap: 5px;
    column-gap: 10px;
    p {
      margin-bottom: 0;
    }
    .label {
      font-weight: 500;
      font-size: 0.9rem;
      color: #a9afbc;
      display: flex;
      align-items: center;
      margin-bottom: 0px;

      @media (min-width: $breakpoint-tablet) {
        flex-basis: 20%;
      }
    }
    .value {
      font-weight: 400;
      font-size: 0.9rem;
      justify-content: center;
      color: #414f5f;
      text-align: right;
    }
    .copy-button {
      img {
        width: 0.9rem;
      }
    }

    @media (min-width: $breakpoint-tablet) {
      justify-content: flex-start;
      column-gap: 20px;
    }
  }
}

button.tx-back-button {
  margin-top: 10px;
  margin-bottom: 30px;
  padding: 5px 0;
  font-weight: 500;
  font-size: 0.9rem;
  letter-spacing: -0.005em;
}
