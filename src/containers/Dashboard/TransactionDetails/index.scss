@import 'styles/kpy-custom/variables';

.txn-detail {
  padding: 0;

  .amount-heading {
    margin-top: -5px;
    color: #292b2c;
    font-size: 2rem;
    font-weight: 600;

    span {
      font-size: 1.2rem;
      margin-left: 5px;
      color: rgba(0, 0, 0, 0.2);
    }
  }

  .content-box {
    .target-ref {
      color: #2376f3;
      cursor: pointer;
    }

    .header-row {
      padding: 0px 0px !important;

      button {
        margin-top: 10px;
        padding: 0;
        font-weight: 500;
        font-size: 0.9rem;
        letter-spacing: -0.005em;
      }

      .action-btns-container {
        display: flex;

        button {
          margin-top: 0;
          padding: 8px 10px;
          font-size: 0.9rem;
          margin-left: 10px;
          min-height: 35px;
          font-weight: 500;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;

          &:first-of-type {
            margin-left: 0;
          }

          &.btn-light-blue {
            background-color: #eaf2fe;
          }
        }
      }

      .invoice-heading {
        display: flex;
        margin-top: 2.2rem;
        font-style: normal;
        border-bottom: 0.5px solid #a9afbc;
        justify-content: space-between;
        align-items: flex-end;
        flex-direction: column;
        margin-bottom: 10px;
        padding-bottom: 10px;

        @media (min-width: $breakpoint-tablet) {
          flex-direction: row;
        }

        .invoice-info {
          display: flex;
          justify-content: space-between;

          h3 {
            padding-right: 0.5rem;
            font-weight: 600;
            font-size: 1.5rem;
          }

          .invoice-date {
            color: #c4c4c4;
            font-size: 0.9rem;

            span {
              border-radius: 5px;
              padding: 4px 10px;
              font-size: 0.85rem;
              font-weight: 500;
              margin-left: 0.5rem;
            }
          }
        }

        #update-refund {
          display: flex;
          flex-basis: 30%;
          align-items: center !important;
          justify-content: flex-end !important;

          @media (min-width: $breakpoint-desktop) {
            flex-basis: 20%;
          }

          button {
            padding: 8px 10px;
            font-size: 15px;
            font-weight: 400;
            border: none;
          }
        }
      }
    }

    .transaction-details-container {
      display: flex;
      border: none;
      padding: 0px;
      margin-top: 15px !important;
      justify-content: space-between;
      flex-direction: column;

      @media (min-width: $breakpoint-tablet) {
        flex-direction: row;
      }

      p {
        color: #636c72;
        font-weight: 500;
      }

      .copy-button {
        background: none;
        border: none;
        cursor: pointer;

        img {
          width: 0.9rem;
        }
      }

      .refund-heading {
        display: flex;
        justify-content: space-between;
        margin: 4.5rem 0 1rem;

        button {
          background: none;
          border: none;
          font-weight: 500;
          color: $kpyblue;

          img {
            width: 1rem;
            margin-left: 5px;
          }
        }
      }

      .refund-list {
        > li {
          padding: 1.1rem 0 0.8rem !important;
          display: block !important;
        }

        #pending-circles {
          opacity: 0.7;
          width: 1rem;
        }

        section {
          padding: unset;
        }

        .accordion-content {
          padding: 2rem 0 1rem;
        }
      }

      section {
        flex-grow: 1;
        padding: 2rem;
        font-size: 0.9rem;
        border-radius: 10px;
        background: #f1f6fa;
        font-weight: 500;

        &:first-of-type {
          padding: 2rem 2rem 2rem 1rem;
          background: white;
        }
      }

      .section-heading {
        color: rgb(16, 38, 73);
        font-weight: 600;
        font-size: 1rem;
        padding: 0;
      }

      .trxn-information {
        padding: 0px !important;

        summary {
          display: flex;
          align-items: center;
          justify-content: space-between;

          p {
            margin: 0;
            font-weight: 500;

            &:last-of-type {
              font-weight: 600;
              color: #102649;
            }
          }
        }

        .accordion-content {
          padding: unset;
        }

        .trxn-breakdown-list {
          display: flex;
          list-style-type: none;
          padding: 0px;
          height: fit-content;
          justify-content: space-between;

          .merchant-name {
            background: none;
            border: none;
            font-weight: 500;
            color: #2376f3;
            padding: 0;
          }

          .btn-original {
            color: #2376f3 !important;
            padding: 0.375rem 0rem;
            font-size: 0.85rem;
          }

          p {
            padding-left: 0.3rem;
            width: max-content;
            margin-right: 0.4rem;
            font-weight: 300;
            font-size: 0.9rem;
            color: #414f5f;
            letter-spacing: -0.003em;

            @media (max-width: $breakpoint-tablet) {
              margin-top: -0.7rem;
            }

            &:last-of-type {
              margin-top: -0.7rem;
              font-weight: 600;
              font-size: 0.9rem;
              justify-content: center;
              color: #3e4b5b;

              @media (max-width: $breakpoint-tablet) {
                font-size: 0.8rem;
                font-weight: 400;
              }
            }
          }

          li {
            display: inline;
            width: max-width;
            height: 50px;
            padding-left: 0.4rem;
            border-left: 1px solid #dde2ec;

            &:first-of-type {
              border: 0;
              padding-left: 0;
            }
          }

          .reason {
            display: flex;
            position: relative;
            margin-top: 0.2rem;
            border: none;
            width: 160px;
            justify-content: center;
            align-items: center;

            &.failed {
              summary {
                p {
                  &:last-of-type {
                    color: #f32345;
                  }
                }
              }
            }

            &[open] {
              width: fit-content;
              max-width: 250px;
              height: fit-content;
            }

            .accordion-content {
              padding: 1rem;
              background: #f2f4f8;
              margin-top: -0.6rem;
              padding: 0.7rem 1rem;
              border-radius: 0.313rem 0.313rem;
            }
          }
        }
      }

      .customer-information {
        @media (min-width: $breakpoint-tablet) {
          width: 50%;
        }

        @media (min-width: $breakpoint-desktop) {
          max-width: 38%;
        }

        .text-tooltip-w {
          div.text-tooltip--content {
            width: fit-content;
            padding: 0.3rem 0.8rem;
          }
        }

        p {
          display: flex;
          justify-content: space-between;

          span {
            display: block;

            &:last-of-type {
              margin-left: 1.5rem;
              word-break: break-all;
              text-align: right;
              color: rgba(16, 38, 73, 0.4);
            }
          }
        }

        ul {
          list-style-type: none;
          padding: 0;

          li {
            border-bottom: 1px solid rgba(16, 38, 73, 0.2);
            padding-bottom: 1.2rem;
            margin-bottom: 1.2rem;

            &:last-of-type {
              border: none;
              padding: 0;
              margin: 0;
            }
          }
        }
      }

      @media (max-width: $breakpoint-tablet) {
        flex-direction: row;

        .trxn-information {
          width: 100%;

          .trxn-breakdown-list {
            width: 100%;
            display: inline;
            list-style-type: none;
            padding: 0px;
            height: fit-content;

            p {
              display: flex;
              justify-content: flex-start;
              flex-basis: 50%;
              color: #a9afbc;

              &:last-of-type {
                justify-content: flex-end;
                font-weight: 400;
                color: #414f5f;
              }
            }

            li {
              display: flex;
              width: 100%;
              padding-left: 0;
              border-left: none;
              margin-left: 0;
              height: auto;
              text-align: right;
            }
          }
        }
      }
    }
  }

  #net_amount-info {
    color: white;
    font-weight: 400;
    font-size: 0.8rem;
  }

  .copy-button-payref {
    img {
      width: 1.2em !important;
      margin-top: -7px !important;
    }
  }

  #wallet-icon {
    margin: -3px 0.7rem 0 0;
  }

  #mobile-money-icon {
    width: 1rem;
  }
}

.initial-payment-reference {
  background: none;
  border: none;
  font-weight: 500;
  color: #2376f3;
  padding: 0;
}

.underline {
  // background: red;
  margin-bottom: 0;
  border-bottom: 1px solid #dde2ec;
}

.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 2rem;
}

.retry-button {
  width: 90%;
  height: 43px;
  background: #cde5fe;
  border-radius: 8px;
  border: none;
  /* Button/Modal/Bold */

  font-family: 'Averta PE';
  font-style: normal;
  font-weight: 600;
  font-size: 1rem;
  line-height: 37px;
  /* identical to box height, or 185% */
  letter-spacing: -0.005em;

  /* Kora Brand/Blue */

  color: #2376f3;

  &:disabled,
  &.disabled {
    opacity: 0.4;
  }
}

.update-button {
  width: 8%;
  height: 43px;
  background: #2376f3;
  border: none;
  border-radius: 8px;
  color: #cde5fe;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: normal;
  font-weight: 600;
  font-size: 1.4rem;
  line-height: 37px;
  padding-bottom: 10px;

  &:disabled,
  &.disabled {
    opacity: 0.4;
  }
}

.attempt-modal {
  h6 {
    color: #3e4b5b;
    letter-spacing: -0.003em;
    font-weight: 600;
    font-size: 13px;
  }

  p {
    color: #3e4b5b;
    opacity: 0.7;
    font-weight: 400;
    font-size: 13px;
    text-align: left !important;
  }
}

.attempt-id {
  color: #2376f3 !important;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  background: inherit;
  border: none;
}

.attempt-modal-wrap {
  display: flex;
  align-items: center;
  gap: 5px;
}

.attempt-modal-div {
  display: flex;
  flex-direction: column;
}

.attempt-modal-blue {
  letter-spacing: 0.0863636px;
  color: #2376f3 !important;
  font-weight: 600;
  font-size: 13px;
  opacity: 1 !important;
  text-align: left !important;
}

.dot {
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background-color: #d9d9d9;
}

.date-span {
  font-weight: 400;
  font-size: 0.9rem;
  line-height: 23px;
  letter-spacing: -0.0019em;
  color: #aabdce;
}

.account-name {
  font-family: 'Averta PE';
  font-style: normal;
  font-weight: 500;
  font-size: 13px;
  line-height: 23px;
  color: #24b314 !important;
  margin-top: 5px;
}

.approve-modal-content {
  background: #f1f6fa;
  border-radius: 6px;
  width: 100%;
  padding: 10px 20px;
}

.modal-list {
  li {
    display: flex !important;
    justify-content: unset !important;
    border: none !important;
  }
}

.approve-modal-list {
  list-style: none;
  text-align: left !important;
  display: flex !important;
  justify-content: space-between;

  p {
    color: #102649;
    mix-blend-mode: normal;
    opacity: 0.4;
  }

  p:last-child {
    color: #636c72;
    font-style: normal;
    font-weight: 600;
    opacity: 1;
  }
}

.modal-left-content {
  width: 60%;
}

.transaction-details__comp_2 {
  margin-top: 20px;

  .transaction-details-container-2 {
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    @media (min-width: $breakpoint-tablet) {
      flex-direction: row;
    }

    p {
      color: #4e555b;
      font-weight: 500;
    }

    .section-heading {
      padding: 5px 0;
      font-weight: 700;
      margin-top: 25px;
      font-size: 16px !important;
      border-bottom: 0.5px solid #a9afbc;

      .view-breakdown {
        all: unset;
        color: #2376f3;
        cursor: pointer;
        font-size: 1rem;

        &:hover {
          transform: translate(-1%);
        }
      }
    }

    .customer-information {
      flex-grow: 1;
      padding: 0;

      @media (min-width: $breakpoint-tablet) {
        width: 100%;
      }

      @media (min-width: $breakpoint-desktop) {
        max-width: 100%;
      }

      p {
        font-weight: 500;
        display: flex;
        font-size: 0.9rem;
        justify-content: flex-start;

        > span {
          flex-basis: 20%;
          display: block;
          color: #a9afbc;

          &:last-of-type {
            @media (max-width: $breakpoint-tablet) {
              text-align: right !important;
              flex-basis: 60%;
            }

            margin-left: 1.5rem;
            word-break: break-all;
            text-align: left !important;
            color: #414f5f;
            font-weight: 400;
            flex-basis: 30%;
          }

          @media (max-width: $breakpoint-tablet) {
            flex-basis: 50%;
          }

          @media (min-width: $breakpoint-desktop) {
            max-width: 100%;
          }
        }
      }

      ul {
        list-style-type: none;
        padding: 0;

        li {
          &:last-of-type {
            border: none;
            padding: 0;
            margin: 0;
          }

          #bank-icon {
            width: 1rem;
            margin-bottom: 7px;
            margin-right: 10px;
          }

          .card-logo {
            width: 2rem;
            margin-right: 8px;
          }

          .copy-button {
            background: none;
            border: none;
            cursor: pointer;

            img {
              width: 0.9rem;
            }
          }

          .refund-heading {
            display: flex;
            justify-content: flex-start;

            button {
              background: none;
              border: none;
              font-weight: 500;
              color: $kpyblue;
              padding: 0;
              margin-left: 1rem;

              img {
                width: 1rem;
                margin-left: 5px;
              }
            }
          }

          .refund-list {
            > li {
              font-size: 14px !important;
              display: block !important;
              margin: 0 !important;
            }

            #pending-circles {
              opacity: 0.7;
              width: 1rem;
            }

            section {
              padding: unset;
            }

            .accordion-content {
              padding: 2rem 0 1rem;
            }
          }
        }
      }
    }
  }
}

.back-to-top {
  display: flex;
  height: 200px;
  width: 100%;
  align-items: center;
  justify-content: center;

  .btn {
    padding: 5px 10px;
    font-weight: 500;
    letter-spacing: 0.086px;
    color: #414f5f;
    border-radius: 20px;
    background: #f9fbfd;
    box-shadow: 0px 5px 7px rgba(126, 142, 177, 0.1);
  }
}

.rversal-rfund-cback {
  padding-top: 10px;
  display: flex;

  @media (max-width: $breakpoint-desktop) {
    padding-top: 10px;
    display: block;
  }

  @media (min-width: $breakpoint-desktop) {
    max-width: 100%;
  }

  #reversals-tabs-list {
    width: 20%;
    min-width: 160px;

    @media (max-width: $breakpoint-desktop) {
      width: 100%;
    }

    ul {
      border-bottom: none !important;
      width: 100%;

      @media (max-width: $breakpoint-desktop) {
        display: flex;
      }

      .tab-label {
        font-size: 0.9rem;
        font-weight: 400;
      }

      .nav-item {
        margin-bottom: 0px;
        padding-bottom: 0px;
        margin-top: 0px;
        width: 100%;

        @media (max-width: $breakpoint-desktop) {
          width: auto;
        }
      }

      .nav-item.active {
        background: #f1f6fa;
        @media (max-width: $breakpoint-desktop) {
          background: none;
        }
      }
    }

    .nav-link {
      padding: 12px 20px 12px 20px;
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;

      @media (max-width: $breakpoint-desktop) {
        padding: 12px 0px 12px 0px;
        width: auto;
        font-weight: 800;
        margin-right: 1.2rem;
      }

      &::after {
        display: none;
      }

      .tab-icon {
        border-top: 3px solid #a9afbc;
        border-right: 3px solid #a9afbc;
        width: 10px;
        height: 10px;
        transform: rotate(45deg);

        @media (max-width: $breakpoint-desktop) {
          display: none;
        }
      }

      .tab-icon.active {
        border-color: #2376f3;
      }
    }

    .nav-link.active {
      border-left: 3px solid #2376f3;
      padding-left: 17px;

      @media (max-width: $breakpoint-desktop) {
        border-left: none;
        border-bottom: 3px solid #2376f3;
        padding-left: 0px;
      }
    }
  }

  .render-tabs {
    width: 80%;
    border-left: 2px solid #f1f6fa;
  }
}

.status-update-icon {
  background: none;
  border: none;
}

@mixin related-transactions-item-style {
  border: 1px solid #1026493d !important;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem !important;

  @media (min-width: 1280px) {
    align-items: center;
    border: 0 !important;
    border-radius: 0;
    flex-direction: row;
    justify-content: space-between;
    gap: 1.5rem;
    padding: 0 !important;

    @media (min-width: 1560px) {
      gap: 4rem;
    }
  }
}

.related-transactions {
  .more-trxn-heading {
    display: flex;
    justify-content: space-between;
    margin: 1.875rem 0 1.25rem;

    a {
      margin-left: auto;
      font-size: 1rem;
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    &-item {
      @include related-transactions-item-style;

      div:nth-of-type(1) {
        color: #a9afbc;
        font-weight: 500;
      }

      div:nth-of-type(2) {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        span:nth-of-type(1) {
          font-size: 0.8rem;
          font-weight: 500;
        }
        span:nth-of-type(2) {
          color: #a9afbc;
        }
      }

      div:nth-of-type(3) {
        align-items: center;
        display: flex;
        gap: 0.5rem;

        span:nth-of-type(1) {
          align-items: center;
          display: flex;
          gap: 5px;
        }

        span:nth-of-type(2) {
          color: #a9afbc;
          font-weight: 500;
          text-transform: capitalize;
        }
      }

      div:nth-of-type(4) {
        font-weight: 500;
      }
    }

    &-item--conversions {
      @include related-transactions-item-style;

      div:nth-of-type(1) {
        align-items: center;
        color: #a9afbc;
        display: flex;
        font-weight: 500;
        gap: 0.5rem;
      }

      div:nth-of-type(2) {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        span:nth-of-type(1) {
          font-size: 0.8rem;
          font-weight: 500;
        }
        span:nth-of-type(2) {
          color: #a9afbc;
        }
      }

      div:nth-of-type(3) {
        font-weight: 500;
      }

      div:nth-of-type(4) {
        color: #a9afbc;
        font-weight: 500;
      }
    }
  }
}

@media (min-width: 1280px) {
  .related-transactions {
    margin-top: 3rem;

    .more-trxn-heading {
      display: flex;
      justify-content: space-between;
      margin: 1.875rem 0 1.25rem;
    }

    &__list {
      display: flex;
      gap: 1rem;

      &-item {
        div:nth-of-type(1) {
          flex-basis: 14%;
        }

        div:nth-of-type(2) {
          flex-basis: 50%;
        }

        div:nth-of-type(3) {
          align-items: center;
          display: flex;
          gap: 0.5rem;
          flex-basis: 18%;

          span:nth-of-type(1) {
            gap: 0.75rem;
          }
        }

        div:nth-of-type(4) {
          flex-basis: 12%;
          text-align: right;
        }
      }
    }
  }
}

@media (min-width: 1560px) {
  .related-transactions {
    &__list {
      &-item {
        div:nth-of-type(2) {
          gap: 1rem;

          span:nth-of-type(1) {
            font-size: 0.9rem;
          }
          span:nth-of-type(2) {
            font-weight: 400;
          }
        }

        div:nth-of-type(3) {
          gap: 1.5rem;
        }

        div:nth-of-type(4) {
          font-size: 0.9rem;
        }
      }
    }
  }
}
