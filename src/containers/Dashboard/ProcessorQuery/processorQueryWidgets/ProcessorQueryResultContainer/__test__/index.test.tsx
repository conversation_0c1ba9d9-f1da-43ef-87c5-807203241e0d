import React, { Dispatch, SetStateAction } from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { mockedProcessorQuery } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { ProcessorQueryType } from '+types';

import ProcessorQuery from '..';

const mockSetQuery = vi.fn();

const ProcessorQueryResultContainer = ({
  isLoading = false,
  queries = [mockedProcessorQuery.data[0]],
  views = {},
  setViews = vi.fn()
}: {
  isLoading?: boolean;
  queries?: ProcessorQueryType[];
  views?: { [key: string]: boolean };
  setViews?: Dispatch<SetStateAction<{ [key: string]: boolean }>>;
}) => {
  return (
    <MockIndex>
      <ProcessorQuery
        views={views}
        setViews={setViews}
        queries={queries}
        isLoading={isLoading}
        processor="wema"
        setQueries={mockSetQuery}
      />
    </MockIndex>
  );
};

const moreThanOneQuery = (num = 3) => [
  ...Array(num)
    .fill('')
    .map((_, i) => ({ ...mockedProcessorQuery.data[0], reference: `test-${i}` }))
];

describe('ProcessorQueryResultContainer', () => {
  test('component is accessible', async () => {
    const { container } = render(<ProcessorQueryResultContainer />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should render one query response component with the right data', () => {
    render(
      <ProcessorQueryResultContainer
        queries={[{ ...mockedProcessorQuery.data[0], response: 'response received', reference: 'test1' }]}
        views={{ test1: true }}
      />
    );

    expect(screen.getByTestId('processor-query-result-container')).toBeInTheDocument();

    expect(screen.getAllByTestId('processor-query-response')).toHaveLength(1);

    expect(screen.getByText(/clear response/i)).toBeInTheDocument();
    expect(screen.getByText('Query Response from Processor (Wema)')).toBeInTheDocument();

    expect(screen.getByText('Push All Responses')).toBeDisabled();
    expect(screen.getByText('"response received"')).toBeInTheDocument();
    expect(screen.queryByTestId('query-pagination')).not.toBeInTheDocument();

    expect(screen.getByText(/copy code/i)).toBeInTheDocument();
  });

  test('should be able to clear single response', async () => {
    render(<ProcessorQueryResultContainer />);

    expect(screen.getAllByTestId('processor-query-response')).toHaveLength(1);

    await userEvent.click(screen.getByTestId('remove-single-processor'));
    await userEvent.click(screen.getByText('Yes, Remove'));

    await waitFor(() => expect(mockSetQuery).toHaveBeenCalledTimes(1));
  });

  test('should render the ProcessorQueryResultPlaceholder if no query result data is present', async () => {
    render(<ProcessorQueryResultContainer queries={[]} />);

    const placeholder = within(screen.getByTestId('query-result-placeholder'));

    expect(await placeholder.findByText('Query Result')).toBeInTheDocument();
    expect(await placeholder.findByText('Query result details will show here')).toBeInTheDocument();
    expect(await placeholder.findByText('Simply enter the processor ID and click on query to see results.')).toBeInTheDocument();
  });

  test('should render the pagination if more than one query result is rendered', () => {
    render(<ProcessorQueryResultContainer queries={moreThanOneQuery()} />);

    expect(screen.getByTestId('query-pagination')).toBeInTheDocument();
  });

  test('should disable the back buttons if the paginated list is at page (1)', () => {
    render(<ProcessorQueryResultContainer queries={moreThanOneQuery()} />);

    expect(screen.getByTestId('first-page-pagination-btn')).toBeDisabled();
    expect(screen.getByTestId('prev-page-pagination-btn')).toBeDisabled();
  });

  test('should disable the next buttons if the paginated list is less than 6', () => {
    render(<ProcessorQueryResultContainer queries={moreThanOneQuery()} />);

    expect(screen.getByTestId('last-page-pagination-btn')).toBeDisabled();
    expect(screen.getByTestId('next-page-pagination-btn')).toBeDisabled();
  });

  test('should enable the next buttons if the paginated list is  greater than 25', () => {
    render(<ProcessorQueryResultContainer queries={moreThanOneQuery(26)} />);

    expect(screen.getByTestId('last-page-pagination-btn')).not.toBeDisabled();
    expect(screen.getByTestId('next-page-pagination-btn')).not.toBeDisabled();
  });
  test('should enable the prev buttons if user paginates to the next page ', async () => {
    render(<ProcessorQueryResultContainer queries={moreThanOneQuery(26)} />);

    await userEvent.click(screen.getByTestId('last-page-pagination-btn'));

    expect(screen.getByTestId('first-page-pagination-btn')).not.toBeDisabled();
    expect(screen.getByTestId('prev-page-pagination-btn')).not.toBeDisabled();
  });

  test('repush processor button should be disabled if the query does not return a "cleared": true response', async () => {
    render(<ProcessorQueryResultContainer />);

    expect(screen.getByTestId('repush-processor-btn')).toHaveAttribute('aria-disabled', 'true');
  });

  test('should be able to repush if a query if  "cleared": true is returned in the query response and render the done check mark', async () => {
    render(<ProcessorQueryResultContainer queries={[{ ...mockedProcessorQuery.data[0], cleared: true }]} />);

    expect(screen.getByTestId('repush-processor-btn')).toHaveAttribute('aria-disabled', 'false');

    await userEvent.click(screen.getByTestId('repush-processor-btn'));
  });

  test('Hide repush button and cleared status if the query was not successful', async () => {
    render(<ProcessorQueryResultContainer queries={[{ ...mockedProcessorQuery.data[0], cleared: true, success: false }]} />);

    expect(screen.queryByTestId('repush-processor-btn')).not.toBeInTheDocument();
  });

  test('render the done check mark if repush is successful', async () => {
    render(<ProcessorQueryResultContainer queries={[{ ...mockedProcessorQuery.data[0], cleared: true }]} />);

    await userEvent.click(screen.getByTestId('repush-processor-btn'));

    expect(await screen.findByText('Done')).toBeInTheDocument();
  });
});
