@import 'styles/kpy-custom/variables';

.currency-exchange__container {
  .content-box {
    @media (max-width: $breakpoint-tablet) {
      padding: 0;
      margin: 1rem;

      button {
        margin-left: 1rem;
      }
      @media (max-width: 600px) {
        button {
          margin-left: 0;
      }
      }
    }

    .os-tabs-virtual {
      @media (max-width: 400px) {
        flex-direction: column;
      }
    }
  }
}

.filter-button {
  display: flex;
  align-items: center;
  position: static;
  margin-top: 1rem;
  background: none;
  border: none;
  color: #2376F3;
  font-weight: 500;

  &:hover {
    background: none;
    border: none;
    outline: none;
    color: #2376F3;
  }
  
  i + span {
    background: none;
    margin-left: 0.5rem;
    &:hover{
      background: none;
    }
  }
}

@media (min-width: $breakpoint-desktop) {
  
}
