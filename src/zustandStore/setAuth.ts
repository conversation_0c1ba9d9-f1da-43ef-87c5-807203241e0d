import { StateCreator } from 'zustand';
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';

import { Storage } from '+services/storage-services';
import { decryptContent, encryptContent, sessionKeys } from '+utils';

export type AuthSlice = {
  clientToken: string;
  userToken: string;
  refreshToken: string;
  userTokenExpiration: string;
  isAuthenticated: boolean;
  isAuthorized: boolean;
  authorizeData: Record<string, unknown>;
  isLoading: boolean;
  error: boolean;
  profile: {
    email?: string;
    avatar?: string;
  };
  errors: Record<string, unknown>;
};

const storage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    const item = await decryptContent(sessionStorage.getItem(name));
    return JSON.parse(item || null);
  },
  setItem: async (name: string, value: string): Promise<void> => {
    sessionStorage.setItem(name, await encryptContent(value));
  },
  removeItem: (name: string): void => {
    sessionStorage.removeItem(name);
  }
};

const createAuthSlice: StateCreator<AuthSlice, [], [['zustand/persist', AuthSlice]]> = persist<AuthSlice>(
  () => ({
    clientToken: Storage.clientToken() as string,
    userToken: Storage.checkAuthentication() as string,
    refreshToken: Storage.getRefreshToken() as string,
    userTokenExpiration: Storage.checkExpiration() as string,
    isAuthenticated: !!Storage.checkAuthentication(),
    isAuthorized: false,
    authorizeData: {},
    isLoading: false,
    error: false,
    profile: Storage.getItem(sessionKeys.ADMIN_USER_PROFILE) || {
      email: '',
      avatar: ''
    },
    errors: {}
  }),
  {
    name: 'auth',
    storage: createJSONStorage(() => storage)
  }
);

export default createAuthSlice;
