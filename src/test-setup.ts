import '@testing-library/jest-dom/vitest';

import { QueryCache, setLogger } from 'react-query';
import { toHaveNoViolations } from 'jest-axe';
// eslint-disable-next-line import/no-extraneous-dependencies
import { afterAll, afterEach, beforeAll, vi } from 'vitest';

import { server } from '+mock/mockServers';

const queryCache = new QueryCache();

expect.extend(toHaveNoViolations);
// Establish API mocking before all tests.
beforeAll(() => server.listen({ onUnhandledRequest: 'warn' }));
afterEach(() => {
  queryCache.clear();
  server.resetHandlers();
  vi.clearAllMocks();
  server.resetHandlers();
});

afterEach(() => server.resetHandlers());

// Clean up after the tests are finished.
afterAll(() => {
  server.close();
});

setLogger({
  log: console.log,
  warn: console.warn,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  error: () => {}
});
