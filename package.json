{"name": "korapay-admin", "version": "0.1.0", "private": true, "dependencies": {"@bugsnag/js": "^7.3.5", "@bugsnag/plugin-react": "^7.3.5", "@datadog/browser-rum": "^5.21.0", "@datadog/datadog-ci": "^2.45.1", "@react-pdf/renderer": "^3.3.8", "@types/jest": "^29.5.12", "@types/uuid": "^9.0.1", "axios": "^1.6.8", "bootstrap": "^4.4.1", "chart.js": "^4.4.2", "classnames": "^2.2.6", "cors": "^2.8.5", "country-flag-icons": "^1.5.7", "crypto-js": "^4.1.1", "dayjs": "^1.8.28", "dotenv": "^8.2.0", "express": "^4.17.1", "file-saver": "^2.0.2", "formik": "^2.0.4", "history": "^4.10.1", "jest-axe": "^9.0.0", "jsdom": "^24.1.1", "plop": "^4.0.1", "prop-types": "^15.7.2", "qs": "^6.9.4", "react": "^18.3.1", "react-calendar": "^5.0.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^6.8.0", "react-dom": "^18.3.1", "react-number-format": "^5.4.3", "react-papaparse": "^4.1.0", "react-query": "^3.16.0", "react-redux": "^7.1.3", "react-router-dom": "^5.1.2", "react-select": "^5.7.0", "react-select-event": "^5.5.1", "react-use": "^17.4.0", "redux": "^4.0.4", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "ts-pnp": "^1.2.0", "typescript": "^5.7.2", "use-debounce": "^10.0.0", "uuid": "^9.0.0", "zustand": "^4.3.8"}, "scripts": {"analyze": "source-map-explorer 'build/static/js/*.js'", "start": "node app.js", "post:bugsnag": "node bugsnag.js", "post:css": "purgecss --css build/static/css/*.css --content build/static/index.html build/static/js/*.js --out build/static/css", "start:prod": "npm run build && concurrently npm:post:* && npm run start", "test": "TZ=UTC vitest run --coverage.enabled true", "lint:css": "stylelint '.src/*.jsx", "gen": "plop", "start:dev": "vite", "build": "vite build", "serve-vite": "vite preview", "type-check": "npx tscw --noEmit", "vite-test": "vitest run", "coverage": "NODE_ENV=test vitest run --coverage", "analyse-dependency": "npx depcruise src --include-only ^src --output-type dot | dot -T png  > admin-dependency-graph.png", "lint:all": "eslint --report-unused-disable-directives --fix", "format:all": "prettier --write .", "lint-staged": "lint-staged", "prepare": "husky"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "npm run type-check", "npm run lint:all", "npm run test --"]}, "compilerOptions": {"jsx": "react", "esModuleInterop": true}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.2.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest-axe": "^3.5.9", "@types/node": "^22.10.6", "@types/qs": "^6.9.16", "@types/react": "^18.3.1", "@types/react-datepicker": "^4.19.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.1", "@typescript-eslint/parser": "^5.62.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "bugsnag-build-reporter": "^1.0.3", "bugsnag-sourcemaps": "^1.3.0", "camelcase": "^6.0.0", "concurrently": "^6.0.0", "dotenv-expand": "5.1.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^6.11.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-flowtype": "4.5.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-testing-library": "^7.1.1", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vitest": "^0.5.4", "glob": "^7.1.6", "husky": "^9.1.7", "jest-axe": "^9.0.0", "lint-staged": "^15.3.0", "msw": "^2.7.0", "path": "^0.12.7", "prettier": "^3.3.3", "react-app-polyfill": "^1.0.4", "sass": "~1.32.6", "source-map-explorer": "^2.1.1", "stylelint": "^13.6.1", "tscw-config": "^1.1.1", "typescript": "^5.4.5", "vite": "5.4.6", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.8"}}