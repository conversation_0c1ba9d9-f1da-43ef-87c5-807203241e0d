{"extends": ["eslint:recommended", "plugin:react/recommended", "prettier", "prettier/react", "plugin:@typescript-eslint/recommended", "plugin:import/typescript", "plugin:testing-library/react"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": 2018, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es6": true, "node": true, "jest": true}, "plugins": ["react", "jsx-a11y", "import", "eslint-plugin-prettier", "eslint-plugin-react", "@typescript-eslint", "vitest"], "rules": {"camelcase": "off", "no-unused-vars": "off", "import/no-unresolved": 0, "@typescript-eslint/explicit-function-return-type": "off", "comma-dangle": 0, "react/destructuring-assignment": "off", "react/function-component-definition": "off", "react/prop-types": "off", "react/require-default-props": "off", "prettier/prettier": ["error"], "import/order": "off", "global-require": "off", "react/static-property-placement": ["warn", "static public field"], "no-plusplus": ["error", {"allowForLoopAfterthoughts": true}], "import/no-extraneous-dependencies": ["error", {"devDependencies": true}], "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "no-use-before-define": ["error", {"functions": true, "classes": true, "variables": false}], "jsx-a11y/label-has-associated-control": ["error", {"required": {"some": ["nesting", "id"]}}], "jsx-a11y/label-has-for": ["error", {"required": {"some": ["nesting", "id"]}}], "react/jsx-props-no-spreading": ["error", {"html": "ignore", "custom": "enforce"}], "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never", "json": "never", "": "never"}]}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}, "ignorePatterns": ["dist/*", "node_modules/*"]}