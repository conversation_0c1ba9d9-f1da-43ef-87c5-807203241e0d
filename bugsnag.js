/* eslint-disable import/no-extraneous-dependencies */
const { upload } = require('bugsnag-sourcemaps');
const reportBuild = require('bugsnag-build-reporter');
const glob = require('glob');
const fs = require('fs');
require('dotenv').config();
const appVersion = require('./package.json').version;

// Exposing sourcemaps in production is  security risk but we need to generate sourcemaps for bugsnag
// This script runs after the build step to upload all sourcemaps to bugsnag and delete them afterwards
const findSourceMaps = callback => glob('dist/**/*/*.map', callback);

const uploadSourceMap = sourceMap => {
  const minifiedFile = sourceMap.replace('.map', '');
  const minifiedFileRelativePath = minifiedFile.split('dist/')[1];
  return upload({
    apiKey: process.env.REACT_APP_HERMES_WINGS,
    appVersion,
    overwrite: true,
    minifiedUrl: `http*://${process.env.REACT_APP_SITE_HOSTNAME || 'business-admin.korapay.com'}/${minifiedFileRelativePath}`,
    sourceMap,
    minifiedFile,
    projectRoot: __dirname,
    uploadSources: true
  });
};

const deleteFiles = files =>
  files.forEach(file => {
    const path = `${__dirname}/${file}`;
    fs.unlinkSync(path);
  });

function notifyRelease() {
  reportBuild({
    apiKey: process.env.REACT_APP_HERMES_WINGS,
    releaseStage: process.env.REACT_APP_ENV,
    appVersion
  })
    .then(() => console.log('Bugsnag build reported'))
    .catch(err => console.log('Reporting Bugsnag build failed', err.messsage));
}

const processSourceMaps = () =>
  findSourceMaps((error, files) =>
    Promise.all(files.map(uploadSourceMap))
      .then(() => {
        deleteFiles(files);
        notifyRelease();
      })
      .catch(e => console.log(e))
  );

processSourceMaps();
